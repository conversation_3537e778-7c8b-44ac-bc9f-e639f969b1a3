use openkey_rust_lib::engine::VietnameseEngine;
use openkey_rust_lib::engine::traits::ProcessResult;

fn main() {
    println!("🧪 Testing 'uoongs' vs 'uongs'");
    println!("===============================");
    
    // Test "uoongs" (with double 'o')
    println!("\n🔍 Testing 'uoongs' (should become 'uống'):");
    let mut engine1 = VietnameseEngine::new();
    let mut result1 = String::new();
    
    for (i, ch) in "uoongs".chars().enumerate() {
        match engine1.process_character(ch) {
            ProcessResult::NoChange => result1.push(ch),
            ProcessResult::Replace { backspace_count, new_chars } => {
                for _ in 0..backspace_count { result1.pop(); }
                result1.extend(new_chars);
            }
            _ => {}
        }
        println!("  After '{}': '{}'", ch, result1);
    }
    
    if result1 == "uống" {
        println!("  ✅ PASS: 'uoongs' → 'uống'");
    } else {
        println!("  ❌ FAIL: 'uoongs' → '{}' (expected: 'uống')", result1);
    }
    
    // Test "uongs" (single 'o')
    println!("\n🔍 Testing 'uongs' (should become 'uống'):");
    let mut engine2 = VietnameseEngine::new();
    let mut result2 = String::new();
    
    for (i, ch) in "uongs".chars().enumerate() {
        match engine2.process_character(ch) {
            ProcessResult::NoChange => result2.push(ch),
            ProcessResult::Replace { backspace_count, new_chars } => {
                for _ in 0..backspace_count { result2.pop(); }
                result2.extend(new_chars);
            }
            _ => {}
        }
        println!("  After '{}': '{}'", ch, result2);
    }
    
    if result2 == "uống" {
        println!("  ✅ PASS: 'uongs' → 'uống'");
    } else {
        println!("  ❌ FAIL: 'uongs' → '{}' (expected: 'uống')", result2);
    }
    
    println!("\n🔍 Analysis:");
    println!("  Vietnamese 'uống' contains 'ố' (o with circumflex and acute)");
    println!("  This suggests the sequence should be:");
    println!("  1. 'uo' + 'o' → 'uô' (circumflex transformation)");
    println!("  2. 'uô' + 'ng' + 's' → 'uống' (tone on 'ô' → 'ố')");
}
