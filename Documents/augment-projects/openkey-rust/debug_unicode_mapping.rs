fn main() {
    println!("🔍 Debugging Unicode Character Mappings from Original OpenKey");
    println!("=============================================================");
    
    // From the original OpenKey Vietnamese.cpp Unicode table
    // KEY_U: {0x0000, 0x0000, 0x01AF, 0x01B0, 0x00DA, 0x00FA, 0x00D9, 0x00F9, 0x1EE6, 0x1EE7, 0x0168, 0x0169, 0x1EE4, 0x1EE5}
    // Index meanings: {CAPS_CHAR, NORMAL_CHAR, CAPS_W_CHAR, NORMAL_W_CHAR, ...tones...}
    
    println!("\n🔤 KEY_U Unicode Mappings:");
    println!("  CAPS_CHAR:    0x0000 = {} (none)", char::from_u32(0x0000).unwrap_or('?'));
    println!("  NORMAL_CHAR:  0x0000 = {} (none)", char::from_u32(0x0000).unwrap_or('?'));
    println!("  CAPS_W_CHAR:  0x01AF = {} (Ư)", char::from_u32(0x01AF).unwrap_or('?'));
    println!("  NORMAL_W_CHAR:0x01B0 = {} (ư)", char::from_u32(0x01B0).unwrap_or('?'));
    
    // KEY_O mappings
    // KEY_O: {0x00D4, 0x00F4, 0x01A0, 0x01A1, 0x00D3, 0x00F3, 0x00D2, 0x00F2, 0x1ECE, 0x1ECF, 0x00D5, 0x00F5, 0x1ECC, 0x1ECD}
    println!("\n🔤 KEY_O Unicode Mappings:");
    println!("  CAPS_CHAR:    0x00D4 = {} (Ô)", char::from_u32(0x00D4).unwrap_or('?'));
    println!("  NORMAL_CHAR:  0x00F4 = {} (ô)", char::from_u32(0x00F4).unwrap_or('?'));
    println!("  CAPS_W_CHAR:  0x01A0 = {} (Ơ)", char::from_u32(0x01A0).unwrap_or('?'));
    println!("  NORMAL_W_CHAR:0x01A1 = {} (ơ)", char::from_u32(0x01A1).unwrap_or('?'));
    
    // KEY_A mappings  
    // KEY_A: {0x00C2, 0x00E2, 0x0102, 0x0103, 0x00C1, 0x00E1, 0x00C0, 0x00E0, 0x1EA2, 0x1EA3, 0x00C3, 0x00E3, 0x1EA0, 0x1EA1}
    println!("\n🔤 KEY_A Unicode Mappings:");
    println!("  CAPS_CHAR:    0x00C2 = {} (Â)", char::from_u32(0x00C2).unwrap_or('?'));
    println!("  NORMAL_CHAR:  0x00E2 = {} (â)", char::from_u32(0x00E2).unwrap_or('?'));
    println!("  CAPS_W_CHAR:  0x0102 = {} (Ă)", char::from_u32(0x0102).unwrap_or('?'));
    println!("  NORMAL_W_CHAR:0x0103 = {} (ă)", char::from_u32(0x0103).unwrap_or('?'));
    
    println!("\n🎯 Key Insights:");
    println!("  1. 'uo' → 'ư': u + o should trigger KEY_U + w modifier → 0x01B0 (ư)");
    println!("  2. 'ow' → 'ơ': o + w should trigger KEY_O + w modifier → 0x01A1 (ơ)");
    println!("  3. 'aa' → 'â': a + a should trigger KEY_A + circumflex → 0x00E2 (â)");
    println!("  4. 'oo' → 'ô': o + o should trigger KEY_O + circumflex → 0x00F4 (ô)");
    
    println!("\n🔍 Testing Current String vs Unicode Approach:");
    
    // Test string replacement approach (current)
    let test_cases = vec![
        ("uo", "ư"),
        ("ow", "ơ"), 
        ("aa", "â"),
        ("oo", "ô"),
    ];
    
    for (input, expected) in test_cases {
        let result_string = input.replace("uo", "ư").replace("ow", "ơ").replace("aa", "â").replace("oo", "ô");
        let expected_unicode = match input {
            "uo" => char::from_u32(0x01B0).unwrap().to_string(), // ư
            "ow" => char::from_u32(0x01A1).unwrap().to_string(), // ơ
            "aa" => char::from_u32(0x00E2).unwrap().to_string(), // â
            "oo" => char::from_u32(0x00F4).unwrap().to_string(), // ô
            _ => expected.to_string(),
        };
        
        println!("  '{}' → String: '{}', Unicode: '{}', Match: {}", 
                input, result_string, expected_unicode, result_string == expected_unicode);
    }
    
    println!("\n🧪 Testing Tone Applications:");
    // Test tone applications with Unicode approach
    // From original: tone indices are at positions 4-13 in the array
    // Positions: 4-5 (acute), 6-7 (grave), 8-9 (hook), 10-11 (tilde), 12-13 (dot)
    
    let u_tones = [
        (0x00DA, 0x00FA), // acute: Ú, ú  
        (0x00D9, 0x00F9), // grave: Ù, ù
        (0x1EE6, 0x1EE7), // hook: Ủ, ủ
        (0x0168, 0x0169), // tilde: Ũ, ũ
        (0x1EE4, 0x1EE5), // dot: Ụ, ụ
    ];
    
    // For ư (0x01B0), we need to find the toned versions
    // This would require a separate tone mapping table
    
    println!("  U + acute: {} {}", char::from_u32(u_tones[0].0).unwrap(), char::from_u32(u_tones[0].1).unwrap());
    println!("  U + grave: {} {}", char::from_u32(u_tones[1].0).unwrap(), char::from_u32(u_tones[1].1).unwrap());
    println!("  U + hook:  {} {}", char::from_u32(u_tones[2].0).unwrap(), char::from_u32(u_tones[2].1).unwrap());
    println!("  U + tilde: {} {}", char::from_u32(u_tones[3].0).unwrap(), char::from_u32(u_tones[3].1).unwrap());
    println!("  U + dot:   {} {}", char::from_u32(u_tones[4].0).unwrap(), char::from_u32(u_tones[4].1).unwrap());
    
    println!("\n💡 CONCLUSION:");
    println!("  The current Rust implementation uses string replacements,");
    println!("  but the original OpenKey uses Unicode code point mappings.");
    println!("  This could explain transformation inconsistencies!");
}
