use openkey_rust_lib::engine::vietnamese::VietnameseProcessor;
use openkey_rust_lib::engine::traits::{KeyEvent, KeyEventType, InputMethodProcessor};
use openkey_rust_lib::engine::ProcessResult;

fn main() {
    println!("🔍 Testing Vietnamese processor with backspace functionality");
    
    let mut processor = VietnameseProcessor::new();
    let mut result_text = String::new();
    
    // Test 1: Type "vaa" -> "vâ", then backspace
    println!("\n=== Test 1: Type 'vaa' -> 'vâ', then backspace ===");
    
    // Type 'v'
    let result1 = processor.process_character('v');
    match result1 {
        ProcessResult::NoChange => result_text.push('v'),
        _ => println!("Unexpected result for 'v': {:?}", result1),
    }
    println!("After 'v': '{}'", result_text);
    
    // Type 'a'
    let result2 = processor.process_character('a');
    match result2 {
        ProcessResult::NoChange => result_text.push('a'),
        _ => println!("Unexpected result for first 'a': {:?}", result2),
    }
    println!("After 'va': '{}'", result_text);
    
    // Type second 'a' -> should transform to "vâ"
    let result3 = processor.process_character('a');
    match result3 {
        ProcessResult::Replace { new_chars, backspace_count } => {
            // Remove backspace_count characters
            for _ in 0..backspace_count {
                result_text.pop();
            }
            // Add new characters
            result_text.extend(new_chars.iter());
            println!("Replace: bs={}, new_chars={:?} -> '{}'", backspace_count, new_chars, result_text);
        },
        _ => println!("Unexpected result for second 'a': {:?}", result3),
    }
    
    // Now test backspace using KeyEvent
    println!("\n--- Testing backspace with KeyEvent ---");
    let backspace_event = KeyEvent {
        key_code: 51, // macOS backspace
        modifiers: 0,
        timestamp: std::time::Instant::now(),
        event_type: KeyEventType::KeyDown,
    };
    
    println!("Before backspace: buffer='{}', result_text='{}'", processor.get_buffer_content(), result_text);
    
    // Process backspace through the Vietnamese processor's key event handler
    use openkey_rust_lib::engine::traits::{InputContext, ProcessorSettings};
    use openkey_rust_lib::engine::performance::PerformanceMonitor;

    let mut context = InputContext {
        current_state: openkey_rust_lib::engine::state::InputState::new(),
        settings: ProcessorSettings::default(),
        performance_monitor: std::sync::Arc::new(PerformanceMonitor::new()),
    };

    let backspace_result = processor.process_key(backspace_event, &mut context);
    match backspace_result {
        ProcessResult::ReplaceText { text, backspace_count } => {
            // Remove backspace_count characters
            for _ in 0..backspace_count {
                result_text.pop();
            }
            // Add replacement text
            result_text.push_str(&text);
            println!("Backspace result: ReplaceText {{ text: '{}', backspace_count: {} }} -> '{}'", text, backspace_count, result_text);
        },
        ProcessResult::PassThrough => {
            // Just remove one character
            result_text.pop();
            println!("Backspace result: PassThrough -> '{}'", result_text);
        },
        other => {
            println!("Backspace result: {:?}", other);
        }
    }
    
    println!("After backspace: buffer='{}', result_text='{}'", processor.get_buffer_content(), result_text);
    
    // Test 2: Direct handle_backspace method
    println!("\n=== Test 2: Direct handle_backspace method ===");
    processor.reset_buffer();
    result_text.clear();
    
    // Type "vaa" -> "vâ" again
    let _r1 = processor.process_character('v');
    result_text.push('v');
    let _r2 = processor.process_character('a');
    result_text.push('a');
    let result3 = processor.process_character('a');
    match result3 {
        ProcessResult::Replace { new_chars, backspace_count } => {
            for _ in 0..backspace_count {
                result_text.pop();
            }
            result_text.extend(new_chars.iter());
        },
        _ => {}
    }
    
    println!("Before direct backspace: buffer='{}', result_text='{}'", processor.get_buffer_content(), result_text);
    
    // Call handle_backspace directly
    let backspace_result = processor.handle_backspace();
    match backspace_result {
        Some(new_buffer_content) => {
            println!("Direct backspace result: Some('{}') -> buffer now contains '{}'", new_buffer_content, processor.get_buffer_content());
            // Simulate removing one character from result_text
            result_text.pop();
            println!("Result text after simulated backspace: '{}'", result_text);
        },
        None => {
            println!("Direct backspace result: None (buffer was empty)");
        }
    }
    
    println!("\n🎯 Backspace functionality test completed!");
    println!("Final buffer: '{}'", processor.get_buffer_content());
    println!("Final result text: '{}'", result_text);
}
