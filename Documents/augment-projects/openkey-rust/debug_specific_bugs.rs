use openkey_rust_lib::engine::{VietnameseEngine, InputMethod};
use openkey_rust_lib::engine::traits::ProcessResult;

fn main() {
    println!("🔍 Debug Specific Vietnamese Transformation Bugs");
    println!("=================================================");
    
    // Test the specific failing transformations
    let test_cases = vec![
        ("uong", "uống", "uong should become uống (u + o + n + g + tone)"),
        ("nuowc", "nước", "nuowc should become nước (n + u + o + w + c → n + ư + ớ + c)"),
        ("lowf", "lời", "lowf should become lời (l + o + w + f → l + ơ + i + f → lời)"),
        ("caau", "câu", "caau should become câu (c + a + a + u → c + â + u)"),
        ("tooi", "tôi", "tooi should become tôi (t + o + o + i → t + ô + i)"),
    ];
    
    for (input, expected, description) in test_cases {
        println!("\n🧪 Testing: {} → {} ({})", input, expected, description);
        println!("{}", "=".repeat(60));
        
        let mut engine = VietnameseEngine::new();
        engine.set_input_method(InputMethod::Telex);
        
        let mut result = String::new();
        println!("Step-by-step processing:");
        
        for (i, ch) in input.chars().enumerate() {
            let process_result = engine.process_character(ch);
            println!("  Step {}: Processing '{}' - Current buffer: '{}'", i + 1, ch, result);
            
            match process_result {
                ProcessResult::NoChange => {
                    result.push(ch);
                    println!("    → NoChange: '{}'", result);
                }
                ProcessResult::Replace { backspace_count, new_chars } => {
                    let chars: Vec<char> = result.chars().collect();
                    if chars.len() >= backspace_count as usize {
                        result = chars[..chars.len() - backspace_count as usize].iter().collect();
                    }
                    for new_ch in new_chars {
                        result.push(new_ch);
                    }
                    println!("    → Replace (bs:{}): '{}'", backspace_count, result);
                    engine.reset_after_injection();
                }
                ProcessResult::PassThrough => {
                    result.push(ch);
                    println!("    → PassThrough: '{}'", result);
                }
                _ => {
                    result.push(ch);
                    println!("    → Other: '{}'", result);
                }
            }
        }
        
        let success = result == expected;
        let status = if success { "✅ PASS" } else { "❌ FAIL" };
        println!("\n🎯 Final Result: '{}' → {} (expected: '{}')", result, status, expected);
        
        if !success {
            println!("🔍 Analysis needed for: {}", description);
        }
    }
    
    println!("\n🔍 Additional Pattern Analysis");
    println!("==============================");
    
    // Test individual vowel combinations
    let vowel_tests = vec![
        ("uo", "ư", "uo should become ư"),
        ("ow", "ơ", "ow should become ơ"),
        ("aa", "â", "aa should become â"),
        ("oo", "ô", "oo should become ô"),
    ];
    
    for (input, expected, description) in vowel_tests {
        let mut engine = VietnameseEngine::new();
        engine.set_input_method(InputMethod::Telex);
        
        let mut result = String::new();
        for ch in input.chars() {
            let process_result = engine.process_character(ch);
            match process_result {
                ProcessResult::NoChange => result.push(ch),
                ProcessResult::Replace { backspace_count, new_chars } => {
                    let chars: Vec<char> = result.chars().collect();
                    if chars.len() >= backspace_count as usize {
                        result = chars[..chars.len() - backspace_count as usize].iter().collect();
                    }
                    for new_ch in new_chars {
                        result.push(new_ch);
                    }
                    engine.reset_after_injection();
                }
                ProcessResult::PassThrough => result.push(ch),
                _ => result.push(ch),
            }
        }
        
        let success = result == expected;
        let status = if success { "✅" } else { "❌" };
        println!("  {} '{}' → '{}' (expected: '{}') - {}", status, input, result, expected, description);
    }
    
    println!("\n🔍 Tone Application Analysis");
    println!("=============================");
    
    // Test tone applications
    let tone_tests = vec![
        ("af", "à", "a + f should become à"),
        ("owf", "ờ", "ow + f should become ờ"),
        ("owif", "ời", "owi + f should become ời"),
        ("uongs", "uống", "uong + s should become uống"),
    ];
    
    for (input, expected, description) in tone_tests {
        let mut engine = VietnameseEngine::new();
        engine.set_input_method(InputMethod::Telex);
        
        let mut result = String::new();
        for ch in input.chars() {
            let process_result = engine.process_character(ch);
            match process_result {
                ProcessResult::NoChange => result.push(ch),
                ProcessResult::Replace { backspace_count, new_chars } => {
                    let chars: Vec<char> = result.chars().collect();
                    if chars.len() >= backspace_count as usize {
                        result = chars[..chars.len() - backspace_count as usize].iter().collect();
                    }
                    for new_ch in new_chars {
                        result.push(new_ch);
                    }
                    engine.reset_after_injection();
                }
                ProcessResult::PassThrough => result.push(ch),
                _ => result.push(ch),
            }
        }
        
        let success = result == expected;
        let status = if success { "✅" } else { "❌" };
        println!("  {} '{}' → '{}' (expected: '{}') - {}", status, input, result, expected, description);
    }
}
