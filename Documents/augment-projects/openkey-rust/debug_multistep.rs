use openkey_rust_lib::engine::{VietnameseEngine, traits::ProcessResult};

fn main() {
    println!("🔍 Debug Multi-Step Vietnamese Transformations");
    println!("==============================================");
    
    let mut engine = VietnameseEngine::new();
    
    // Test the problematic sequence "toof" step by step
    println!("Testing 'toof' step by step:");
    println!("Expected: t -> to -> tô -> tôf -> tồ");
    println!();
    
    let mut current_output = String::new();
    let sequence = "toof";
    
    for (step, ch) in sequence.chars().enumerate() {
        println!("Step {}: Processing '{}'", step + 1, ch);
        // println!("  Current buffer: {:?}", engine.get_buffer_debug());
        println!("  Current output: '{}'", current_output);

        match engine.process_character(ch) {
            ProcessResult::NoChange => {
                current_output.push(ch);
                println!("  Result: NoChange -> '{}'", current_output);
            }
            ProcessResult::Replace { backspace_count, new_chars } => {
                // Apply backspaces
                let chars: Vec<char> = current_output.chars().collect();
                if chars.len() >= backspace_count as usize {
                    current_output = chars[..chars.len() - backspace_count as usize].iter().collect();
                }
                // Add new text
                let new_text: String = new_chars.iter().collect();
                current_output.push_str(&new_text);
                println!("  Result: Replace (bs:{}) -> '{}'", backspace_count, current_output);

                // Reset engine after injection (simulating real behavior)
                engine.reset_after_injection();
            }
            ProcessResult::PassThrough => {
                current_output.push(ch);
                println!("  Result: PassThrough -> '{}'", current_output);
            }
            _ => {
                current_output.push(ch);
                println!("  Result: Other -> '{}'", current_output);
            }
        }
        // println!("  Buffer after processing: {:?}", engine.get_buffer_debug());
        println!();
    }
    
    println!("Final result: '{}'", current_output);
    println!("Expected: 'tồ'");
    println!("Success: {}", current_output == "tồ");
    
    println!();
    println!("==============================================");
    
    // Test another sequence
    engine.reset();
    current_output.clear();
    
    println!("Testing 'tooi' step by step:");
    println!("Expected: t -> to -> too -> tô -> tooi -> tôi");
    println!();

    let sequence = "tooi";
    
    for (step, ch) in sequence.chars().enumerate() {
        println!("Step {}: Processing '{}'", step + 1, ch);
        // println!("  Current buffer: {:?}", engine.get_buffer_debug());
        println!("  Current output: '{}'", current_output);

        match engine.process_character(ch) {
            ProcessResult::NoChange => {
                current_output.push(ch);
                println!("  Result: NoChange -> '{}'", current_output);
            }
            ProcessResult::Replace { backspace_count, new_chars } => {
                // Apply backspaces
                let chars: Vec<char> = current_output.chars().collect();
                if chars.len() >= backspace_count as usize {
                    current_output = chars[..chars.len() - backspace_count as usize].iter().collect();
                }
                // Add new text
                let new_text: String = new_chars.iter().collect();
                current_output.push_str(&new_text);
                println!("  Result: Replace (bs:{}) -> '{}'", backspace_count, current_output);

                // Reset engine after injection (simulating real behavior)
                engine.reset_after_injection();
            }
            ProcessResult::PassThrough => {
                current_output.push(ch);
                println!("  Result: PassThrough -> '{}'", current_output);
            }
            _ => {
                current_output.push(ch);
                println!("  Result: Other -> '{}'", current_output);
            }
        }
        // println!("  Buffer after processing: {:?}", engine.get_buffer_debug());
        println!();
    }
    
    println!("Final result: '{}'", current_output);
    println!("Expected: 'tôi'");
    println!("Success: {}", current_output == "tôi");
}
