use openkey_rust_lib::engine::vietnamese::VietnameseProcessor;
use openkey_rust_lib::engine::InputMethod;

fn main() {
    println!("🔍 Testing single 'u' processing");

    let mut processor = VietnameseProcessor::new();

    // Test what process_telex does with just ['u']
    let chars = vec!['u'];
    let result = processor.process_telex(&chars);

    println!("Input: {:?}", chars);
    println!("Result: '{}'", result);
    println!("Result chars: {:?}", result.chars().collect::<Vec<char>>());
}
