use openkey_rust_lib::engine::vietnamese::VietnameseProcessor;
use openkey_rust_lib::engine::ProcessResult;

fn main() {
    println!("🔍 Debugging 'uong' processing step by step");
    
    let mut processor = VietnameseProcessor::new();
    let mut result_text = String::new();
    
    let input = "uong";
    println!("Input: '{}'", input);
    
    for (i, ch) in input.chars().enumerate() {
        println!("\n--- Step {}: Processing '{}' ---", i + 1, ch);
        println!("Before: '{}'", result_text);
        
        let result = processor.process_character(ch);
        
        match result {
            ProcessResult::NoChange => {
                result_text.push(ch);
                println!("NoChange: Added '{}' -> '{}'", ch, result_text);
            },
            ProcessResult::Replace { new_chars, backspace_count } => {
                // Handle backspaces properly
                for _ in 0..backspace_count {
                    result_text.pop();
                }
                result_text.extend(new_chars.iter());
                println!("Replace: bs={}, new_chars={:?} -> '{}'", 
                        backspace_count, new_chars, result_text);
            },
            ProcessResult::ReplaceText { text, backspace_count } => {
                // Handle backspaces properly
                for _ in 0..backspace_count {
                    result_text.pop();
                }
                result_text.push_str(&text);
                println!("ReplaceText: bs={}, text='{}' -> '{}'", 
                        backspace_count, text, result_text);
            },
            ProcessResult::Append { text } => {
                result_text.push_str(&text);
                println!("Append: '{}' -> '{}'", text, result_text);
            },
            ProcessResult::Delete { count } => {
                for _ in 0..count {
                    result_text.pop();
                }
                println!("Delete: {} chars -> '{}'", count, result_text);
            },
            ProcessResult::PassThrough => {
                result_text.push(ch);
                println!("PassThrough: Added '{}' -> '{}'", ch, result_text);
            },
            _ => {
                result_text.push(ch);
                println!("Other: {:?} -> '{}'", result, result_text);
            }
        }
    }
    
    println!("\n🎯 Final result: '{}' (expected: 'uong')", result_text);
    
    if result_text == "uong" {
        println!("✅ SUCCESS: 'uong' preserved correctly");
    } else {
        println!("❌ FAILURE: Expected 'uong', got '{}'", result_text);
    }
}
