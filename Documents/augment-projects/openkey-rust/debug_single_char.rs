use openkey_rust_lib::engine::vietnamese::VietnameseProcessor;
use openkey_rust_lib::engine::ProcessResult;

fn main() {
    println!("🔍 Debugging single character processing");
    
    let mut processor = VietnameseProcessor::new();
    
    println!("Initial buffer: '{}'", processor.get_buffer_content());
    println!("Initial buffer chars: {:?}", processor.get_current_buffer());
    
    println!("\n--- Processing single 'u' ---");
    let result = processor.process_character('u');
    
    println!("Result: {:?}", result);
    println!("Final buffer: '{}'", processor.get_buffer_content());
    println!("Final buffer chars: {:?}", processor.get_current_buffer());
}
