use openkey_rust_lib::engine::{VietnameseProcessor, InputMethod, Encoding, LanguageMode, ProcessResult};

fn main() {
    println!("🧪 Testing Final Vietnamese Transformation Bugs");
    println!("===============================================");
    
    let mut processor = VietnameseProcessor::new();
    processor.input_method = InputMethod::Telex;
    processor.encoding = Encoding::Unicode;
    processor.language_mode = LanguageMode::Vietnamese;
    
    println!("\n✅ FIXED ISSUES:");
    test_sequence(&mut processor, "nuowc", "nước", "✅ PASS");
    test_sequence(&mut processor, "lowf", "lời", "✅ PASS");
    
    println!("\n❌ REMAINING ISSUES:");
    test_sequence(&mut processor, "uongs", "uống", "❌ FAIL");
    test_sequence(&mut processor, "uo", "ư", "❌ FAIL");

    println!("\n🔍 DEBUGGING 'uo' transformation:");
    test_sequence(&mut processor, "uo ", "ư ", "🔍 DEBUG");
    
    println!("\n🔍 ADDITIONAL TESTS:");
    test_sequence(&mut processor, "uong", "uong", "✅ PASS (should remain uong)");
    test_sequence(&mut processor, "owf", "ờ", "❓ CHECK");
    
    println!("\n📊 SUMMARY:");
    println!("  ✅ Fixed: nuowc → nước, lowf → lời");
    println!("  ❌ Remaining: uongs → uống, uo → ư");
    println!("  🎯 Focus: Tone application to 'uo' sequences and standalone 'uo' transformation");
}

fn test_sequence(processor: &mut VietnameseProcessor, input: &str, expected: &str, status: &str) {
    processor.reset_buffer();
    
    let mut result_text = String::new();
    
    for ch in input.chars() {
        let result = processor.process_character(ch);
        
        match result {
            ProcessResult::NoChange => {
                result_text.push(ch);
            },
            ProcessResult::Replace { new_chars, backspace_count } => {
                // Handle backspaces properly
                for _ in 0..backspace_count {
                    result_text.pop();
                }
                result_text.extend(new_chars);
            },
            ProcessResult::ReplaceText { text, backspace_count } => {
                // Handle backspaces properly
                for _ in 0..backspace_count {
                    result_text.pop();
                }
                result_text.push_str(&text);
            },
            ProcessResult::Append { text } => {
                result_text.push_str(&text);
            },
            ProcessResult::Delete { count: _ } => {
                // Handle deletion
            },
            ProcessResult::PassThrough => {
                result_text.push(ch);
            },
            _ => {
                // Handle other cases
                result_text.push(ch);
            }
        }
    }
    
    let matches = result_text == expected;
    let status_icon = if matches { "✅" } else { "❌" };
    let actual_status = if matches { "✅ PASS" } else { "❌ FAIL" };

    println!("  {} Testing: '{}' → '{}' (expected: '{}') {}",
             status_icon, input, result_text, expected, actual_status);
}
