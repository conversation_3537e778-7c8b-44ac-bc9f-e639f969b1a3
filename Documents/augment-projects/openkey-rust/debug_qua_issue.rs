use openkey_rust_lib::engine::vietnamese::VietnameseProcessor;
use openkey_rust_lib::engine::ProcessResult;

fn main() {
    println!("🔍 Debugging 'qua' appearing issue");
    println!("===================================");
    
    // Test 1: Fresh processor state
    println!("\n=== Test 1: Fresh processor initialization ===");
    let mut processor = VietnameseProcessor::new();
    
    println!("Initial buffer: '{}'", processor.get_buffer_content());
    println!("Initial debug state: {}", processor.get_buffer_debug());
    
    // Test 2: Process a single character
    println!("\n=== Test 2: Process single character 'a' ===");
    let result = processor.process_character('a');
    println!("Result: {:?}", result);
    println!("Buffer after 'a': '{}'", processor.get_buffer_content());
    println!("Debug state: {}", processor.get_buffer_debug());
    
    // Test 3: Process another character
    println!("\n=== Test 3: Process character 'b' ===");
    let result = processor.process_character('b');
    println!("Result: {:?}", result);
    println!("Buffer after 'ab': '{}'", processor.get_buffer_content());
    println!("Debug state: {}", processor.get_buffer_debug());
    
    // Test 4: Reset and try again
    println!("\n=== Test 4: Reset and try fresh ===");
    processor.reset_buffer();
    println!("Buffer after reset: '{}'", processor.get_buffer_content());
    println!("Debug state after reset: {}", processor.get_buffer_debug());
    
    let result = processor.process_character('x');
    println!("Result for 'x': {:?}", result);
    println!("Buffer after 'x': '{}'", processor.get_buffer_content());
    
    // Test 5: Check if there's any hidden state
    println!("\n=== Test 5: Check for hidden state ===");
    let buffer_chars = processor.get_current_buffer();
    println!("Buffer as Vec<char>: {:?}", buffer_chars);
    println!("Buffer length: {}", buffer_chars.len());
    
    for (i, ch) in buffer_chars.iter().enumerate() {
        println!("  [{}]: '{}' (U+{:04X})", i, ch, *ch as u32);
    }
    
    // Test 6: Try typing "qua" sequence to see if it matches
    println!("\n=== Test 6: Type 'qua' sequence ===");
    processor.reset_buffer();
    
    let mut result_text = String::new();
    for ch in "qua".chars() {
        println!("Processing '{}'...", ch);
        let result = processor.process_character(ch);
        match result {
            ProcessResult::NoChange => {
                result_text.push(ch);
                println!("  NoChange -> '{}'", result_text);
            },
            ProcessResult::Replace { new_chars, backspace_count } => {
                for _ in 0..backspace_count {
                    result_text.pop();
                }
                result_text.extend(new_chars.iter());
                println!("  Replace -> '{}'", result_text);
            },
            other => {
                result_text.push(ch);
                println!("  Other ({:?}) -> '{}'", other, result_text);
            }
        }
        println!("  Buffer: '{}'", processor.get_buffer_content());
    }
    
    println!("\nFinal result: '{}'", result_text);
    
    // Test 7: Check if there's any pre-existing content in the processor
    println!("\n=== Test 7: Check processor internals ===");
    let fresh_processor = VietnameseProcessor::new();
    println!("Fresh processor buffer: '{}'", fresh_processor.get_buffer_content());
    println!("Fresh processor debug: {}", fresh_processor.get_buffer_debug());
    
    // Test if the issue is in the CLI itself
    println!("\n=== Test 8: Simulate CLI behavior ===");
    let mut cli_processor = VietnameseProcessor::new();
    
    // Simulate what might happen in CLI initialization
    println!("CLI processor initial state:");
    println!("  Buffer: '{}'", cli_processor.get_buffer_content());
    println!("  Debug: {}", cli_processor.get_buffer_debug());
    
    // Try processing an empty input or special character
    println!("\nTesting edge cases that might cause 'qua':");
    
    // Test null character
    let null_result = cli_processor.process_character('\0');
    println!("Null char result: {:?}", null_result);
    println!("Buffer after null: '{}'", cli_processor.get_buffer_content());
    
    // Test space character
    cli_processor.reset_buffer();
    let space_result = cli_processor.process_character(' ');
    println!("Space char result: {:?}", space_result);
    println!("Buffer after space: '{}'", cli_processor.get_buffer_content());
    
    println!("\n🎯 Investigation complete. Check if any of these states show 'qua'.");
}
