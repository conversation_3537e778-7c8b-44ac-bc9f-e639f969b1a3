use openkey_rust_lib::engine::vietnamese::VietnameseProcessor;
use openkey_rust_lib::engine::ProcessResult;

fn main() {
    println!("🔍 Testing Correct Vietnamese Telex Patterns");
    println!("============================================");
    
    let test_cases = vec![
        // Correct patterns
        ("uw", "ư", "uw should become ư"),
        ("ow", "ơ", "ow should become ơ"),
        ("uow", "ươ", "uow should become ươ"),
        ("uo", "uo", "uo should remain uo (no transformation)"),
        
        // Other basic patterns
        ("aa", "â", "aa should become â"),
        ("oo", "ô", "oo should become ô"),
        ("aw", "ă", "aw should become ă"),
        ("ee", "ê", "ee should become ê"),
    ];
    
    for (input, expected, description) in test_cases {
        println!("\n🧪 Testing: '{}' → '{}' ({})", input, expected, description);
        
        let mut processor = VietnameseProcessor::new();
        let mut result_text = String::new();
        
        for ch in input.chars() {
            let result = processor.process_character(ch);
            
            match result {
                ProcessResult::NoChange => {
                    result_text.push(ch);
                },
                ProcessResult::Replace { new_chars, backspace_count } => {
                    // Handle backspaces properly
                    for _ in 0..backspace_count {
                        result_text.pop();
                    }
                    result_text.extend(new_chars.iter());
                },
                ProcessResult::PassThrough => {
                    result_text.push(ch);
                },
                _ => {
                    result_text.push(ch);
                }
            }
        }
        
        let success = result_text == expected;
        let status = if success { "✅ PASS" } else { "❌ FAIL" };
        println!("   Result: '{}' → {} (expected: '{}')", result_text, status, expected);
        
        if !success {
            println!("   🔍 Need to investigate: {}", description);
        }
    }
    
    println!("\n🔍 Testing Complex Patterns");
    println!("============================");
    
    let complex_cases = vec![
        ("uongs", "uống", "uongs should become uống (uo + ng + s tone)"),
        ("nuowc", "nước", "nuowc should become nước (n + uow + c)"),
    ];
    
    for (input, expected, description) in complex_cases {
        println!("\n🧪 Testing: '{}' → '{}' ({})", input, expected, description);
        
        let mut processor = VietnameseProcessor::new();
        let mut result_text = String::new();
        
        for ch in input.chars() {
            let result = processor.process_character(ch);
            
            match result {
                ProcessResult::NoChange => {
                    result_text.push(ch);
                },
                ProcessResult::Replace { new_chars, backspace_count } => {
                    // Handle backspaces properly
                    for _ in 0..backspace_count {
                        result_text.pop();
                    }
                    result_text.extend(new_chars.iter());
                },
                ProcessResult::PassThrough => {
                    result_text.push(ch);
                },
                _ => {
                    result_text.push(ch);
                }
            }
        }
        
        let success = result_text == expected;
        let status = if success { "✅ PASS" } else { "❌ FAIL" };
        println!("   Result: '{}' → {} (expected: '{}')", result_text, status, expected);
        
        if !success {
            println!("   🔍 Need to investigate: {}", description);
        }
    }
}
