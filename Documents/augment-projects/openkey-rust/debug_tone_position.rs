use openkey_rust_lib::engine::VietnameseEngine;
use openkey_rust_lib::engine::traits::ProcessResult;

fn main() {
    println!("🔍 Debug Tone Position Detection");
    println!("=================================");
    
    let mut engine = VietnameseEngine::new();
    let mut result = String::new();
    
    // Process "uongs" step by step
    println!("\n🧪 Processing 'uongs':");
    
    for (i, ch) in "uongs".chars().enumerate() {
        println!("\nStep {}: Processing '{}'", i + 1, ch);
        println!("  Current result: '{}'", result);
        
        match engine.process_character(ch) {
            ProcessResult::NoChange => {
                result.push(ch);
                println!("  → NoChange: '{}'", result);
            }
            ProcessResult::Replace { backspace_count, new_chars } => {
                println!("  → Replace (bs:{}): removing {} chars", backspace_count, backspace_count);
                for _ in 0..backspace_count { 
                    if let Some(removed) = result.pop() {
                        println!("    Removed: '{}'", removed);
                    }
                }
                let new_str: String = new_chars.iter().collect();
                result.extend(new_chars);
                println!("  → Added: '{}' → Final: '{}'", new_str, result);
            }
            ProcessResult::PassThrough => {
                result.push(ch);
                println!("  → PassThrough: '{}'", result);
            }
            other => {
                println!("  → Other result: {:?}", other);
            }
        }
    }
    
    println!("\n🎯 Final Result: '{}'", result);
    if result == "uống" {
        println!("✅ PASS: 'uongs' → 'uống'");
    } else {
        println!("❌ FAIL: 'uongs' → '{}' (expected: 'uống')", result);
    }
    
    // Test the individual components
    println!("\n🔍 Testing tone position detection logic:");
    println!("Expected behavior:");
    println!("  1. 'uongs' should identify 'uong' pattern");
    println!("  2. Tone position should be at 'u' (position 0)");
    println!("  3. 'u' + 'o' should be replaced with 'ướ' (uo + acute tone)");
    println!("  4. Final result: 'ướ' + 'ng' = 'uống'");
}
