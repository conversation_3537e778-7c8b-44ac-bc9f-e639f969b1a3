use openkey_rust_lib::engine::vietnamese::VietnameseProcessor;
use openkey_rust_lib::engine::ProcessResult;

fn main() {
    println!("🔍 Debugging 'uongs' processing step by step");
    
    let mut processor = VietnameseProcessor::new();
    let mut result_text = String::new();
    
    let input = "uongs";
    println!("Input: '{}'", input);
    
    for (i, ch) in input.chars().enumerate() {
        println!("\n--- Step {}: Processing '{}' ---", i + 1, ch);
        println!("Before: '{}'", result_text);
        println!("Buffer before: '{}'", processor.get_buffer_content());
        
        let result = processor.process_character(ch);
        
        match result {
            ProcessResult::NoChange => {
                result_text.push(ch);
                println!("NoChange: Added '{}' -> '{}'", ch, result_text);
            },
            ProcessResult::Replace { new_chars, backspace_count } => {
                // Handle backspaces properly
                for _ in 0..backspace_count {
                    result_text.pop();
                }
                result_text.extend(new_chars.iter());
                println!("Replace: bs={}, new_chars={:?} -> '{}'", 
                        backspace_count, new_chars, result_text);
            },
            ProcessResult::ReplaceText { text, backspace_count } => {
                // Handle backspaces properly
                for _ in 0..backspace_count {
                    result_text.pop();
                }
                result_text.push_str(&text);
                println!("ReplaceText: bs={}, text='{}' -> '{}'", 
                        backspace_count, text, result_text);
            },
            ProcessResult::PassThrough => {
                result_text.push(ch);
                println!("PassThrough: Added '{}' -> '{}'", ch, result_text);
            },
            _ => {
                result_text.push(ch);
                println!("Other: {:?} -> '{}'", result, result_text);
            }
        }
        
        println!("Buffer after: '{}'", processor.get_buffer_content());
    }
    
    println!("\n🎯 Final result: '{}' (expected: 'uống')", result_text);
    
    if result_text == "uống" {
        println!("✅ SUCCESS: 'uongs' transformed to 'uống'");
    } else {
        println!("❌ FAILURE: Expected 'uống', got '{}'", result_text);
    }
}
