// Enhanced Vietnamese Engine State Reset Test
// Tests comprehensive scenarios including punctuation, formatting, and realistic Vietnamese text
// This test ensures the character buffer corruption fix works in all practical use cases

use openkey_rust_lib::engine::{
    traits::ProcessResult,
    vietnamese::VietnameseProcessor,
    InputMethod, LanguageMode, Encoding,
};

fn main() {
    println!("🧪 Enhanced Vietnamese Engine State Reset Test");
    println!("==============================================");
    println!();

    // First, let's test the specific transformation issue
    test_simple_tone_transformations();

    // Run all test scenarios
    test_basic_scenario();
    test_punctuation_boundaries();
    test_line_breaks_and_formatting();
    test_realistic_vietnamese_text();
    test_mixed_scenarios();

    println!("🎉 All Vietnamese engine state reset tests completed!");
}

fn test_simple_tone_transformations() {
    println!("🔍 Debug Test: Correct Telex Tone Transformations");
    println!("=================================================");
    println!("Testing correct Telex sequences for common Vietnamese words");
    println!();

    let mut processor = create_test_processor();

    // Test correct Telex sequences for common Vietnamese words
    let test_cases = vec![
        ("tooi", "tôi", "tooi should become tôi (I/me - correct Telex sequence)"),
        ("nois", "nói", "nois should become nói (to speak)"),
        ("cos", "có", "cos should become có (to have)"),
        // Test tone mark 'f' (grave accent) correctly
        ("toof", "tồ", "toof should become tồ (too + f = tô + f = tồ)"),
        ("noof", "nồ", "noof should become nồ (noo + f = nô + f = nồ)"),
        ("coof", "cồ", "coof should become cồ (coo + f = cô + f = cồ)"),
        ("luowif", "lười", "luowif should become lười (luo + w + i + f = luơi + f = lười)"),
    ];

    for (input, expected, description) in test_cases {
        println!("Testing: {} → {} ({})", input, expected, description);

        // Reset processor for each test
        processor.reset_buffer();

        // Process each character individually
        let mut result = String::new();
        for ch in input.chars() {
            match processor.process_character(ch) {
                ProcessResult::NoChange => {
                    result.push(ch);
                }
                ProcessResult::Replace { new_chars, backspace_count } => {
                    // Apply backspaces
                    for _ in 0..backspace_count {
                        result.pop();
                    }
                    // Add new characters
                    let new_text: String = new_chars.iter().collect();
                    result.push_str(&new_text);

                    // Reset after injection (simulating real behavior)
                    processor.reset_after_injection();
                }
                ProcessResult::PassThrough => {
                    result.push(ch);
                }
                _ => {
                    result.push(ch);
                }
            }
        }

        let success = result == expected;
        println!("  Result: '{}' → {}", result, if success { "✅ PASS" } else { "❌ FAIL" });

        if !success {
            // Let's also test direct transformation
            let direct_result = processor.apply_telex_enhancements(input);
            println!("  Direct transformation: '{}' → '{}'", input, direct_result);
        }

        println!();
    }
}

fn test_basic_scenario() {
    println!("📝 Test 1: Basic Scenario - 'ăn gì chưa vậy ní'");
    println!("================================================");

    let mut processor = create_test_processor();
    println!("Expected: Each word should be processed independently");
    println!("Bug: Engine state corruption causes wrong transformations");
    println!();

    // Test sequence that should produce "ăn gì chưa vậy ní"
    let test_sequence = vec![
        // "ăn" - should work correctly
        ('a', "a"),
        ('w', "ă (aw->ă)"),
        ('n', "n"),
        (' ', "space (word boundary)"),

        // "gì" - should work correctly
        ('g', "g"),
        ('i', "i"),
        ('f', "ì (gif->gì)"),
        (' ', "space (word boundary)"),

        // "chưa" - should work correctly
        ('c', "c"),
        ('h', "h"),
        ('w', "w"),
        ('a', "a (chwa->chưa)"),
        (' ', "space (word boundary)"),

        // "vậy" - should work correctly (vayaj->vậy)
        ('v', "v"),
        ('a', "a"),
        ('y', "y"),
        ('a', "a"),
        ('j', "j (vayaj->vậy)"),
        (' ', "space (word boundary)"),

        // "ní" - THIS IS WHERE THE BUG OCCURS
        // The engine should process this independently, not with corrupted state
        ('n', "n"),
        ('i', "i"),
        ('s', "s (nis->ní)"),
    ];

    let result = run_test_sequence(&mut processor, &test_sequence, "ăn gì chưa vậy ní");
    println!("✅ Test 1 Result: {}", if result { "PASSED" } else { "FAILED" });
    println!();
}

fn test_punctuation_boundaries() {
    println!("📝 Test 2: Punctuation Boundaries");
    println!("==================================");
    println!("Testing comma, period, colon, quotation marks as word boundaries");
    println!();

    let mut processor = create_test_processor();

    // Test sequence: "Xin chào, tôi tên là Minh. Bạn có khỏe không?"
    let test_sequence = vec![
        // "Xin"
        ('X', "X"), ('i', "i"), ('n', "n"),
        (' ', "space"),

        // "chào" (chaf + ow -> chào)
        ('c', "c"), ('h', "h"), ('a', "a"), ('f', "f (chaf->chà)"),
        ('o', "o"), ('w', "w (ow->ò -> chào)"),

        // Comma - should reset engine state
        (',', "comma (punctuation boundary)"),
        (' ', "space"),

        // "tôi" (toof -> tôi)
        ('t', "t"), ('o', "o"), ('o', "o"), ('f', "f (toof->tôi)"),
        (' ', "space"),

        // "tên" (teen -> tên)
        ('t', "t"), ('e', "e"), ('e', "e"), ('n', "n (teen->tên)"),
        (' ', "space"),

        // "là" (laf -> là)
        ('l', "l"), ('a', "a"), ('f', "f (laf->là)"),
        (' ', "space"),

        // "Minh"
        ('M', "M"), ('i', "i"), ('n', "n"), ('h', "h"),

        // Period - should reset engine state
        ('.', "period (sentence boundary)"),
        (' ', "space"),

        // "Bạn" (Banj -> Bạn)
        ('B', "B"), ('a', "a"), ('n', "n"), ('j', "j (Banj->Bạn)"),
        (' ', "space"),

        // "có" (coof -> có)
        ('c', "c"), ('o', "o"), ('o', "o"), ('f', "f (coof->có)"),
        (' ', "space"),

        // "khỏe" (khoer -> khỏe)
        ('k', "k"), ('h', "h"), ('o', "o"), ('e', "e"), ('r', "r (khoer->khỏe)"),
        (' ', "space"),

        // "không" (khoong -> không)
        ('k', "k"), ('h', "h"), ('o', "o"), ('o', "o"), ('n', "n"), ('g', "g (khoong->không)"),

        // Question mark - should reset engine state
        ('?', "question mark (punctuation boundary)"),
    ];

    let expected = "Xin chào, tôi tên là Minh. Bạn có khỏe không?";
    let result = run_test_sequence(&mut processor, &test_sequence, expected);
    println!("✅ Test 2 Result: {}", if result { "PASSED" } else { "FAILED" });
    println!();
}

fn test_line_breaks_and_formatting() {
    println!("📝 Test 3: Line Breaks and Formatting");
    println!("======================================");
    println!("Testing newlines and multi-line text with Vietnamese transformations");
    println!();

    let mut processor = create_test_processor();

    // Test multi-line Vietnamese text with Enter characters
    let test_sequence = vec![
        // Line 1: "Hôm nay trời đẹp."
        ('H', "H"), ('o', "o"), ('o', "o"), ('m', "m (Hoom->Hôm)"),
        (' ', "space"),
        ('n', "n"), ('a', "a"), ('y', "y"),
        (' ', "space"),
        ('t', "t"), ('r', "r"), ('o', "o"), ('w', "w"), ('f', "f (trowf->trời)"),
        (' ', "space"),
        ('d', "d"), ('e', "e"), ('e', "e"), ('p', "p (deep->đẹp)"),
        ('.', "period"),
        ('\n', "newline (line boundary)"),

        // Line 2: "Tôi muốn đi chơi."
        ('T', "T"), ('o', "o"), ('o', "o"), ('f', "f (Toof->Tôi)"),
        (' ', "space"),
        ('m', "m"), ('u', "u"), ('o', "o"), ('n', "n (muon->muốn)"),
        (' ', "space"),
        ('d', "d"), ('i', "i (di->đi)"),
        (' ', "space"),
        ('c', "c"), ('h', "h"), ('o', "o"), ('w', "w"), ('f', "f (chowf->chơi)"),
        ('.', "period"),
        ('\n', "newline (line boundary)"),

        // Line 3: "Bạn có muốn đi không?"
        ('B', "B"), ('a', "a"), ('n', "n"), ('j', "j (Banj->Bạn)"),
        (' ', "space"),
        ('c', "c"), ('o', "o"), ('o', "o"), ('f', "f (coof->có)"),
        (' ', "space"),
        ('m', "m"), ('u', "u"), ('o', "o"), ('n', "n (muon->muốn)"),
        (' ', "space"),
        ('d', "d"), ('i', "i (di->đi)"),
        (' ', "space"),
        ('k', "k"), ('h', "h"), ('o', "o"), ('o', "o"), ('n', "n"), ('g', "g (khoong->không)"),
        ('?', "question mark"),
    ];

    let expected = "Hôm nay trời đẹp.\nTôi muốn đi chơi.\nBạn có muốn đi không?";
    let result = run_test_sequence(&mut processor, &test_sequence, expected);
    println!("✅ Test 3 Result: {}", if result { "PASSED" } else { "FAILED" });
    println!();
}

fn test_realistic_vietnamese_text() {
    println!("📝 Test 4: Realistic Vietnamese Text");
    println!("=====================================");
    println!("Testing a longer, realistic Vietnamese paragraph with quotes and complex punctuation");
    println!();

    let mut processor = create_test_processor();

    // Realistic Vietnamese text: "Mẹ nói: \"Con phải học hành chăm chỉ, đừng lười biếng!\" Tôi trả lời: \"Vâng, con hiểu rồi.\""
    let test_sequence = vec![
        // "Mẹ" (Mej -> Mẹ)
        ('M', "M"), ('e', "e"), ('j', "j (Mej->Mẹ)"),
        (' ', "space"),

        // "nói" (noof -> nói)
        ('n', "n"), ('o', "o"), ('o', "o"), ('f', "f (noof->nói)"),

        // Colon and quote
        (':', "colon (punctuation boundary)"),
        (' ', "space"),
        ('"', "opening quote (punctuation boundary)"),

        // "Con"
        ('C', "C"), ('o', "o"), ('n', "n"),
        (' ', "space"),

        // "phải" (phaif -> phải)
        ('p', "p"), ('h', "h"), ('a', "a"), ('i', "i"), ('f', "f (phaif->phải)"),
        (' ', "space"),

        // "học" (hocj -> học)
        ('h', "h"), ('o', "o"), ('c', "c"), ('j', "j (hocj->học)"),
        (' ', "space"),

        // "hành" (hanhf -> hành)
        ('h', "h"), ('a', "a"), ('n', "n"), ('h', "h"), ('f', "f (hanhf->hành)"),
        (' ', "space"),

        // "chăm" (cham -> chăm)
        ('c', "c"), ('h', "h"), ('a', "a"), ('m', "m (cham->chăm)"),
        (' ', "space"),

        // "chỉ" (chir -> chỉ)
        ('c', "c"), ('h', "h"), ('i', "i"), ('r', "r (chir->chỉ)"),

        // Comma
        (',', "comma (punctuation boundary)"),
        (' ', "space"),

        // "đừng" (duwngf -> đừng)
        ('d', "d"), ('u', "u"), ('w', "w"), ('n', "n"), ('g', "g"), ('f', "f (duwngf->đừng)"),
        (' ', "space"),

        // "lười" (luowf -> lười)
        ('l', "l"), ('u', "u"), ('o', "o"), ('w', "w"), ('f', "f (luowf->lười)"),
        (' ', "space"),

        // "biếng" (bieengs -> biếng)
        ('b', "b"), ('i', "i"), ('e', "e"), ('e', "e"), ('n', "n"), ('g', "g"), ('s', "s (bieengs->biếng)"),

        // Exclamation and closing quote
        ('!', "exclamation (punctuation boundary)"),
        ('"', "closing quote (punctuation boundary)"),
        (' ', "space"),

        // "Tôi" (Toof -> Tôi)
        ('T', "T"), ('o', "o"), ('o', "o"), ('f', "f (Toof->Tôi)"),
        (' ', "space"),

        // "trả" (trar -> trả)
        ('t', "t"), ('r', "r"), ('a', "a"), ('r', "r (trar->trả)"),
        (' ', "space"),

        // "lời" (lowf -> lời)
        ('l', "l"), ('o', "o"), ('w', "w"), ('f', "f (lowf->lời)"),

        // Colon and quote
        (':', "colon (punctuation boundary)"),
        (' ', "space"),
        ('"', "opening quote (punctuation boundary)"),

        // "Vâng" (Vaang -> Vâng)
        ('V', "V"), ('a', "a"), ('a', "a"), ('n', "n"), ('g', "g (Vaang->Vâng)"),

        // Comma
        (',', "comma (punctuation boundary)"),
        (' ', "space"),

        // "con"
        ('c', "c"), ('o', "o"), ('n', "n"),
        (' ', "space"),

        // "hiểu" (hieeur -> hiểu)
        ('h', "h"), ('i', "i"), ('e', "e"), ('e', "e"), ('u', "u"), ('r', "r (hieeur->hiểu)"),
        (' ', "space"),

        // "rồi" (roof -> rồi)
        ('r', "r"), ('o', "o"), ('o', "o"), ('f', "f (roof->rồi)"),

        // Period and closing quote
        ('.', "period (punctuation boundary)"),
        ('"', "closing quote (punctuation boundary)"),
    ];

    let expected = "Mẹ nói: \"Con phải học hành chăm chỉ, đừng lười biếng!\" Tôi trả lời: \"Vâng, con hiểu rồi.\"";
    let result = run_test_sequence(&mut processor, &test_sequence, expected);
    println!("✅ Test 4 Result: {}", if result { "PASSED" } else { "FAILED" });
    println!();
}

fn test_mixed_scenarios() {
    println!("📝 Test 5: Mixed Scenarios - Cross-Contamination Prevention");
    println!("===========================================================");
    println!("Testing Vietnamese transformations immediately before/after punctuation");
    println!();

    let mut processor = create_test_processor();

    // Test critical scenarios where transformations occur right at punctuation boundaries
    let test_sequence = vec![
        // Scenario 1: Transformation right before comma
        // "Tôi ăn cơm, uống nước."
        ('T', "T"), ('o', "o"), ('o', "o"), ('f', "f (Toof->Tôi)"),
        (' ', "space"),
        ('a', "a"), ('w', "w (aw->ă)"), ('n', "n"),
        (' ', "space"),
        ('c', "c"), ('o', "o"), ('w', "w"), ('m', "m (cowm->cơm)"),
        (',', "comma - CRITICAL: should reset engine state"),
        (' ', "space"),

        // Scenario 2: Transformation right after comma
        ('u', "u"), ('o', "o"), ('n', "n"), ('g', "g (uong->uống)"),
        (' ', "space"),
        ('n', "n"), ('u', "u"), ('o', "o"), ('w', "w"), ('c', "c (nuowc->nước)"),
        ('.', "period - CRITICAL: should reset engine state"),
        ('\n', "newline"),

        // Scenario 3: Transformation right before and after quotes
        ('"', "opening quote - CRITICAL: should reset engine state"),
        ('X', "X"), ('i', "i"), ('n', "n"),
        (' ', "space"),
        ('c', "c"), ('h', "h"), ('a', "a"), ('f', "f (chaf->chà)"), ('o', "o"),
        ('"', "closing quote - CRITICAL: should reset engine state"),
        (' ', "space"),

        // Scenario 4: Transformation spanning across punctuation (should NOT happen)
        // This tests that punctuation properly breaks transformation sequences
        ('l', "l"), ('a', "a"), ('f', "f (laf->là)"),
        (' ', "space"),
        ('c', "c"), ('a', "a"), ('u', "u"),
        (' ', "space"),
        ('t', "t"), ('r', "r"), ('a', "a"), ('r', "r (trar->trả)"),
        (' ', "space"),
        ('l', "l"), ('o', "o"), ('w', "w"), ('f', "f (lowf->lời)"),
        (':', "colon - CRITICAL: should reset engine state"),
        (' ', "space"),

        // Scenario 5: Complex transformation after colon
        ('"', "opening quote"),
        ('K', "K"), ('h', "h"), ('o', "o"), ('o', "o"), ('n', "n"), ('g', "g (Khoong->Không)"),
        (' ', "space"),
        ('s', "s"), ('a', "a"), ('o', "o (sao->sao)"),
        ('?', "question mark"),
        ('"', "closing quote"),
    ];

    let expected = "Tôi ăn cơm, uống nước.\n\"Xin chào\" là câu trả lời: \"Không sao?\"";
    let result = run_test_sequence(&mut processor, &test_sequence, expected);
    println!("✅ Test 5 Result: {}", if result { "PASSED" } else { "FAILED" });
    println!();
}

// Helper function to create a properly configured Vietnamese processor
fn create_test_processor() -> VietnameseProcessor {
    let mut processor = VietnameseProcessor::new();
    processor.input_method = InputMethod::Telex;
    processor.encoding = Encoding::Unicode;
    processor.language_mode = LanguageMode::Vietnamese;
    processor
}

// Helper function to run a test sequence and return success/failure
fn run_test_sequence(processor: &mut VietnameseProcessor, test_sequence: &[(char, &str)], expected: &str) -> bool {
    let mut output_buffer = String::new();
    let mut word_count = 0;
    let mut punctuation_resets = 0;

    for (ch, description) in test_sequence {
        println!("Input: '{}' - {}",
                 if *ch == '\n' { "\\n".to_string() } else { ch.to_string() },
                 description);

        let result = processor.process_character(*ch);

        // Simulate what happens in the real system
        match result {
            ProcessResult::NoChange => {
                output_buffer.push(*ch);
                println!("  → NoChange: '{}'", format_output_for_display(&output_buffer));
            }

            ProcessResult::Replace { new_chars, backspace_count } => {
                // Apply backspaces
                for _ in 0..backspace_count {
                    output_buffer.pop();
                }
                // Add new characters
                let new_text: String = new_chars.iter().collect();
                output_buffer.push_str(&new_text);

                println!("  → Replace (bs:{}): '{}' → Current output: '{}'",
                         backspace_count, new_text, format_output_for_display(&output_buffer));

                // CRITICAL: This is where engine state reset should happen in global mode
                // After successful transformation, the engine state should be reset
                // to prevent corruption of subsequent input processing
                processor.reset_after_injection();
                println!("  → Engine state reset after injection");

                word_count += 1;
            }

            ProcessResult::PassThrough => {
                if ch.is_whitespace() || ch.is_ascii_punctuation() || *ch == '"' {
                    output_buffer.push(*ch);
                    if ch.is_ascii_punctuation() || *ch == '"' {
                        punctuation_resets += 1;
                        println!("  → PassThrough (punctuation boundary): '{}'", format_output_for_display(&output_buffer));
                    } else {
                        println!("  → PassThrough (word boundary): '{}'", format_output_for_display(&output_buffer));
                    }
                } else {
                    output_buffer.push(*ch);
                    println!("  → PassThrough: '{}'", format_output_for_display(&output_buffer));
                }
            }

            _ => {
                output_buffer.push(*ch);
                println!("  → Other: '{}'", format_output_for_display(&output_buffer));
            }
        }

        println!();
    }

    println!("🎯 Final Results:");
    println!("================");
    println!("Output: '{}'", format_output_for_display(&output_buffer));
    println!("Words processed: {}", word_count);
    println!("Punctuation boundaries: {}", punctuation_resets);
    println!();

    // Check if the output is correct
    let success = output_buffer.trim() == expected;
    if success {
        println!("✅ SUCCESS: Output matches expected result!");
        println!("   Expected: '{}'", format_output_for_display(expected));
        println!("   Actual:   '{}'", format_output_for_display(&output_buffer.trim()));
    } else {
        println!("❌ FAILURE: Output does not match expected result!");
        println!("   Expected: '{}'", format_output_for_display(expected));
        println!("   Actual:   '{}'", format_output_for_display(&output_buffer.trim()));
        println!();
        println!("🔍 This indicates engine state corruption or boundary handling issues");
    }

    success
}

// Helper function to format output for display (handles newlines)
fn format_output_for_display(text: &str) -> String {
    text.replace('\n', "\\n")
}
