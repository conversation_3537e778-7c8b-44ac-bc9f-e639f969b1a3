// Advanced Telex input method implementation with vi crate integration
// Provides sophisticated Vietnamese input processing with performance optimization

use crate::engine::performance::PerformanceMonitor;
use crate::engine::traits::{
    ConfigError, InputContext, InputMethodProcessor, KeyEvent, ProcessResult, ProcessorConfig,
    ProcessorSettings,
};
use crate::engine::InputState;
use once_cell::sync::Lazy;
use std::collections::{HashMap, VecDeque};
use std::sync::Arc;
use vi::methods::{transform_buffer, TELEX};

/// Advanced Telex processor with vi crate integration and enhanced features
pub struct TelexProcessor {
    // Character buffer for processing
    char_buffer: VecDeque<char>,

    // Configuration
    config: ProcessorConfig,

    // Performance monitoring
    performance_monitor: Arc<PerformanceMonitor>,

    // Telex-specific features
    quick_telex_enabled: bool,
    modern_orthography: bool,
    auto_caps: bool,

    // Processing state
    last_processed_length: usize,
    processing_context: TelexContext,
}

/// Telex processing context for advanced features
#[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON><PERSON>)]
struct TelexContext {
    /// Word start position for capitalization
    word_start: bool,
    /// Last transformation applied
    last_transformation: Option<TelexTransformation>,
}

/// Types of Telex transformations
#[derive(Debug, Clone, PartialEq)]
enum TelexTransformation {
    QuickTelex {
        from: String,
        to: String,
    },
}

impl Default for TelexProcessor {
    fn default() -> Self {
        Self::new()
    }
}

impl TelexProcessor {
    pub fn new() -> Self {
        Self {
            char_buffer: VecDeque::with_capacity(64),
            config: ProcessorConfig::default(),
            performance_monitor: Arc::new(PerformanceMonitor::new()),
            quick_telex_enabled: false,
            modern_orthography: true,
            auto_caps: false,
            last_processed_length: 0,
            processing_context: TelexContext::default(),
        }
    }

    /// Create Telex processor with custom configuration
    pub fn with_config(config: TelexConfig) -> Self {
        let mut processor = Self::new();
        processor.quick_telex_enabled = config.quick_telex;
        processor.modern_orthography = config.modern_orthography;
        processor.auto_caps = config.auto_caps;
        processor
    }

    /// Process input using enhanced Telex method
    pub fn process_telex_enhanced(&mut self, input_chars: &[char]) -> ProcessResult {
        let _timer = crate::time_operation!(self.performance_monitor, {
            self.process_telex_internal(input_chars)
        });

        self.process_telex_internal(input_chars)
    }

    /// Internal Telex processing with advanced features
    fn process_telex_internal(&mut self, input_chars: &[char]) -> ProcessResult {
        // Use vi crate for base processing
        let mut base_result = String::new();
        transform_buffer(&TELEX, input_chars.iter().cloned(), &mut base_result);

        // Apply additional processing layers
        let enhanced_result = self.apply_enhancements(&base_result, input_chars);

        // Calculate transformation result
        let original: String = input_chars.iter().collect();
        if enhanced_result != original {
            let backspace_count = original.len() as u8;
            let new_chars: Vec<char> = enhanced_result.chars().collect();

            ProcessResult::Replace {
                backspace_count,
                new_chars,
            }
        } else {
            ProcessResult::NoChange
        }
    }

    /// Apply enhancement layers on top of vi crate processing
    fn apply_enhancements(&mut self, base_result: &str, input_chars: &[char]) -> String {
        let mut result = base_result.to_string();

        // Apply Quick Telex if enabled
        if self.quick_telex_enabled {
            result = self.apply_quick_telex(&result, input_chars);
        }

        // Apply auto-capitalization if enabled
        if self.auto_caps {
            result = self.apply_auto_caps(&result);
        }

        // Apply modern orthography rules if enabled
        if self.modern_orthography {
            result = self.apply_modern_orthography(&result);
        }

        result
    }

    /// Apply Quick Telex transformations (cc→ch, gg→gi, etc.)
    fn apply_quick_telex(&mut self, text: &str, input_chars: &[char]) -> String {
        let mut result = text.to_string();

        // Check for Quick Telex patterns at the end of input
        if input_chars.len() >= 2 {
            let last_two: String = input_chars[input_chars.len() - 2..].iter().collect();

            if let Some(replacement) = QUICK_TELEX_MAP.get(&last_two) {
                // Replace the last occurrence
                if result.ends_with(&last_two) {
                    result.truncate(result.len() - last_two.len());
                    result.push_str(replacement);

                    // Record transformation
                    self.processing_context.last_transformation =
                        Some(TelexTransformation::QuickTelex {
                            from: last_two,
                            to: replacement.to_string(),
                        });
                }
            }
        }

        result
    }

    /// Apply auto-capitalization rules
    fn apply_auto_caps(&mut self, text: &str) -> String {
        if self.processing_context.word_start && !text.is_empty() {
            let mut chars: Vec<char> = text.chars().collect();
            if let Some(first_char) = chars.first_mut() {
                *first_char = first_char.to_uppercase().next().unwrap_or(*first_char);
            }
            chars.iter().collect()
        } else {
            text.to_string()
        }
    }

    /// Apply modern orthography rules
    fn apply_modern_orthography(&mut self, text: &str) -> String {
        // Modern orthography prefers certain tone mark placements
        // This is a simplified implementation - full rules are complex
        text.to_string()
    }

    /// Reset the processor state
    pub fn reset_state(&mut self) {
        self.char_buffer.clear();
        self.last_processed_length = 0;
        self.processing_context = TelexContext::default();
    }

    /// Check if character is a word boundary for Telex
    pub fn is_telex_word_boundary(&self, ch: char) -> bool {
        matches!(
            ch,
            ' ' | '\t'
                | '\n'
                | '\r'
                | '.'
                | ','
                | ';'
                | ':'
                | '!'
                | '?'
                | '"'
                | '\''
                | '('
                | ')'
                | '['
                | ']'
                | '{'
                | '}'
                | '<'
                | '>'
                | '/'
                | '\\'
                | '|'
        )
    }

    /// Get performance metrics
    pub fn get_performance_metrics(&self) -> crate::engine::performance::PerformanceMetrics {
        self.performance_monitor.get_metrics()
    }
}

/// Configuration for Telex processor
#[derive(Debug, Clone)]
pub struct TelexConfig {
    pub quick_telex: bool,
    pub modern_orthography: bool,
    pub auto_caps: bool,
    pub restore_if_wrong: bool,
}

impl Default for TelexConfig {
    fn default() -> Self {
        Self {
            quick_telex: false,
            modern_orthography: true,
            auto_caps: false,
            restore_if_wrong: true,
        }
    }
}

/// Quick Telex mapping (cc→ch, gg→gi, etc.)
static QUICK_TELEX_MAP: Lazy<HashMap<String, String>> = Lazy::new(|| {
    let mut map = HashMap::new();
    map.insert("cc".to_string(), "ch".to_string());
    map.insert("gg".to_string(), "gi".to_string());
    map.insert("kk".to_string(), "kh".to_string());
    map.insert("nn".to_string(), "ng".to_string());
    map.insert("qq".to_string(), "qu".to_string());
    map.insert("pp".to_string(), "ph".to_string());
    map.insert("tt".to_string(), "th".to_string());
    map.insert("uu".to_string(), "ươ".to_string());
    map
});

impl InputMethodProcessor for TelexProcessor {
    fn name(&self) -> &'static str {
        "Telex"
    }

    fn process_key(&mut self, key: KeyEvent, context: &mut InputContext) -> ProcessResult {
        // Convert key code to character
        let ch = if let Some(c) = char::from_u32(key.key_code as u32) {
            c
        } else {
            return ProcessResult::NoChange;
        };

        // Check if this is a word boundary
        if self.is_telex_word_boundary(ch) {
            // Mark word start for next character
            self.processing_context.word_start = true;
            self.reset_state();
            return ProcessResult::NoChange;
        } else {
            self.processing_context.word_start = false;
        }

        // Only process Vietnamese mode
        if context.current_state.temp_disable {
            return ProcessResult::NoChange;
        }

        // Add character to buffer
        self.char_buffer.push_back(ch);

        // Limit buffer size to prevent memory issues
        if self.char_buffer.len() > 64 {
            self.char_buffer.pop_front();
        }

        // Convert buffer to vector for processing
        let chars: Vec<char> = self.char_buffer.iter().cloned().collect();

        // Process with enhanced Telex
        self.process_telex_enhanced(&chars)
    }

    fn can_handle_key(&self, key: &KeyEvent) -> bool {
        // Can handle printable ASCII characters and some extended characters
        let code = key.key_code;
        (32..=126).contains(&code) || // Basic ASCII
        (128..=255).contains(&code) // Extended ASCII
    }

    fn reset(&mut self) {
        self.reset_state();
    }

    fn reset_after_injection(&mut self) {
        self.reset_state();
    }

    fn get_configuration(&self) -> ProcessorConfig {
        self.config.clone()
    }

    fn set_configuration(&mut self, config: ProcessorConfig) -> Result<(), ConfigError> {
        self.config = config;
        Ok(())
    }

    fn process_char(&mut self, ch: char) -> ProcessResult {
        // Create a dummy KeyEvent and context for compatibility
        let key_event = KeyEvent::new(ch as u16, 0);
        let mut context = InputContext {
            current_state: InputState::default(),
            settings: ProcessorSettings::default(),
            performance_monitor: std::sync::Arc::new(crate::engine::performance::PerformanceMonitor::new()),
        };
        self.process_key(key_event, &mut context)
    }

    fn as_any_mut(&mut self) -> &mut dyn std::any::Any {
        self
    }
}

/// Telex-specific vowel processing utilities
pub struct TelexVowelProcessor;

impl TelexVowelProcessor {
    /// Check if character is a Vietnamese vowel
    pub fn is_vietnamese_vowel(ch: char) -> bool {
        matches!(ch.to_ascii_lowercase(), 'a' | 'e' | 'i' | 'o' | 'u' | 'y')
            || matches!(
                ch,
                'â' | 'ă' | 'ê' | 'ô' | 'ơ' | 'ư' | 'Â' | 'Ă' | 'Ê' | 'Ô' | 'Ơ' | 'Ư'
            )
    }

    /// Get base vowel from Vietnamese vowel with diacritics
    pub fn get_base_vowel(ch: char) -> char {
        match ch {
            'â' | 'ă' | 'Â' | 'Ă' => 'a',
            'ê' | 'Ê' => 'e',
            'ô' | 'ơ' | 'Ô' | 'Ơ' => 'o',
            'ư' | 'Ư' => 'u',
            _ => ch.to_ascii_lowercase(),
        }
    }

    /// Check if character has tone mark
    pub fn has_tone_mark(ch: char) -> bool {
        matches!(
            ch,
            'à' | 'á'
                | 'ả'
                | 'ã'
                | 'ạ'
                | 'ầ'
                | 'ấ'
                | 'ẩ'
                | 'ẫ'
                | 'ậ'
                | 'ằ'
                | 'ắ'
                | 'ẳ'
                | 'ẵ'
                | 'ặ'
                | 'è'
                | 'é'
                | 'ẻ'
                | 'ẽ'
                | 'ẹ'
                | 'ề'
                | 'ế'
                | 'ể'
                | 'ễ'
                | 'ệ'
                | 'ì'
                | 'í'
                | 'ỉ'
                | 'ĩ'
                | 'ị'
                | 'ò'
                | 'ó'
                | 'ỏ'
                | 'õ'
                | 'ọ'
                | 'ồ'
                | 'ố'
                | 'ổ'
                | 'ỗ'
                | 'ộ'
                | 'ờ'
                | 'ớ'
                | 'ở'
                | 'ỡ'
                | 'ợ'
                | 'ù'
                | 'ú'
                | 'ủ'
                | 'ũ'
                | 'ụ'
                | 'ừ'
                | 'ứ'
                | 'ử'
                | 'ữ'
                | 'ự'
                | 'ỳ'
                | 'ý'
                | 'ỷ'
                | 'ỹ'
                | 'ỵ'
        )
    }

    /// Get tone type from Telex tone character
    pub fn get_tone_type(ch: char) -> Option<ToneType> {
        match ch.to_ascii_lowercase() {
            'f' => Some(ToneType::Grave), // huyền
            's' => Some(ToneType::Acute), // sắc
            'r' => Some(ToneType::Hook),  // hỏi
            'x' => Some(ToneType::Tilde), // ngã
            'j' => Some(ToneType::Dot),   // nặng
            _ => None,
        }
    }
}

/// Vietnamese tone types for Telex
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum ToneType {
    Grave, // huyền (f)
    Acute, // sắc (s)
    Hook,  // hỏi (r)
    Tilde, // ngã (x)
    Dot,   // nặng (j)
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::engine::traits::KeyEventType;
    use std::time::Instant;

    #[test]
    fn test_telex_processor_creation() {
        let processor = TelexProcessor::new();
        assert_eq!(processor.name(), "Telex");
        assert!(!processor.quick_telex_enabled);
        assert!(processor.modern_orthography);
    }

    #[test]
    fn test_telex_config() {
        let config = TelexConfig {
            quick_telex: true,
            modern_orthography: true,
            auto_caps: true,
            restore_if_wrong: true,
        };

        let processor = TelexProcessor::with_config(config);
        assert!(processor.quick_telex_enabled);
        assert!(processor.auto_caps);
    }

    #[test]
    fn test_quick_telex_mapping() {
        assert_eq!(QUICK_TELEX_MAP.get("cc"), Some(&"ch".to_string()));
        assert_eq!(QUICK_TELEX_MAP.get("gg"), Some(&"gi".to_string()));
        assert_eq!(QUICK_TELEX_MAP.get("nn"), Some(&"ng".to_string()));
    }

    #[test]
    fn test_vowel_processing() {
        assert!(TelexVowelProcessor::is_vietnamese_vowel('a'));
        assert!(TelexVowelProcessor::is_vietnamese_vowel('â'));
        assert!(TelexVowelProcessor::is_vietnamese_vowel('ư'));
        assert!(!TelexVowelProcessor::is_vietnamese_vowel('b'));

        assert_eq!(TelexVowelProcessor::get_base_vowel('â'), 'a');
        assert_eq!(TelexVowelProcessor::get_base_vowel('ê'), 'e');
        assert_eq!(TelexVowelProcessor::get_base_vowel('ư'), 'u');
    }

    #[test]
    fn test_tone_detection() {
        assert!(TelexVowelProcessor::has_tone_mark('á'));
        assert!(TelexVowelProcessor::has_tone_mark('ề'));
        assert!(!TelexVowelProcessor::has_tone_mark('a'));

        assert_eq!(
            TelexVowelProcessor::get_tone_type('s'),
            Some(ToneType::Acute)
        );
        assert_eq!(
            TelexVowelProcessor::get_tone_type('f'),
            Some(ToneType::Grave)
        );
        assert_eq!(TelexVowelProcessor::get_tone_type('a'), None);
    }

    #[test]
    fn test_word_boundary_detection() {
        let processor = TelexProcessor::new();

        assert!(processor.is_telex_word_boundary(' '));
        assert!(processor.is_telex_word_boundary('.'));
        assert!(processor.is_telex_word_boundary('!'));
        assert!(!processor.is_telex_word_boundary('a'));
        assert!(!processor.is_telex_word_boundary('1'));
    }

    #[test]
    fn test_key_handling() {
        let processor = TelexProcessor::new();

        let key_a = KeyEvent {
            key_code: b'a' as u16,
            modifiers: 0,
            timestamp: Instant::now(),
            event_type: KeyEventType::KeyDown,
        };

        assert!(processor.can_handle_key(&key_a));

        let key_space = KeyEvent {
            key_code: b' ' as u16,
            modifiers: 0,
            timestamp: Instant::now(),
            event_type: KeyEventType::KeyDown,
        };

        assert!(processor.can_handle_key(&key_space));
    }
}
