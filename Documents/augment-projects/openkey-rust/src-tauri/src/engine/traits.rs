// Core trait definitions for the Vietnamese input engine
// Based on the architecture design component interfaces

use crate::engine::performance::PerformanceMonitor;
use crate::engine::state::{CharacterState, InputState};
use serde::{Deserialize, Serialize};
use std::path::Path;
use std::sync::Arc;
use std::time::Instant;
use thiserror::Error;

/// Key event from global input capture
#[derive(Debu<PERSON>, Clone, PartialEq, Eq)]
pub struct KeyEvent {
    pub key_code: u16,
    pub modifiers: u8,
    pub timestamp: Instant,
    pub event_type: KeyEventType,
}

impl KeyEvent {
    pub fn new(key_code: u16, modifiers: u8) -> Self {
        Self {
            key_code,
            modifiers,
            timestamp: Instant::now(),
            event_type: KeyEventType::KeyDown,
        }
    }

    pub fn with_type(mut self, event_type: KeyEventType) -> Self {
        self.event_type = event_type;
        self
    }

    /// Convert key event to character if possible
    pub fn to_char(&self) -> Option<char> {
        #[cfg(target_os = "macos")]
        {
            // Use macOS-specific key code mapping
            self.macos_key_to_char()
        }
        #[cfg(not(target_os = "macos"))]
        {
            // Basic ASCII conversion for other platforms
            if self.key_code >= 32 && self.key_code <= 126 {
                Some(self.key_code as u8 as char)
            } else {
                None
            }
        }
    }

    /// Check if this is a modifier-only key
    pub fn is_modifier_only(&self) -> bool {
        matches!(self.key_code,
            0x10 | 0x11 | 0x12 | // Shift, Ctrl, Alt
            0x5B | 0x5C | 0x5D   // Windows keys
        )
    }

    /// Check if this is a function key
    pub fn is_function_key(&self) -> bool {
        self.key_code >= 0x70 && self.key_code <= 0x87 // F1-F24
    }

    /// Check if this is an arrow key
    pub fn is_arrow_key(&self) -> bool {
        matches!(self.key_code, 0x25 | 0x26 | 0x27 | 0x28) // Left, Up, Right, Down
    }

    /// Check if this is a printable character
    pub fn is_printable(&self) -> bool {
        self.key_code >= 32 && self.key_code <= 126
    }

    /// Check if this is backspace
    pub fn is_backspace(&self) -> bool {
        #[cfg(target_os = "macos")]
        {
            self.key_code == 51 // macOS backspace key code
        }
        #[cfg(not(target_os = "macos"))]
        {
            self.key_code == 0x08 // Standard backspace key code
        }
    }

    /// Check if this is delete
    pub fn is_delete(&self) -> bool {
        #[cfg(target_os = "macos")]
        {
            self.key_code == 117 // macOS delete key code
        }
        #[cfg(not(target_os = "macos"))]
        {
            self.key_code == 0x2E // Standard delete key code
        }
    }

    /// Create KeyEvent from macOS CGEvent
    #[cfg(target_os = "macos")]
    pub fn from_macos_event(key_code: u16, flags: core_graphics::event::CGEventFlags) -> Self {
        // Convert CGEventFlags to our modifier format
        let mut modifiers = 0u8;

        if flags.contains(core_graphics::event::CGEventFlags::CGEventFlagShift) {
            modifiers |= 0x01; // Shift
        }
        if flags.contains(core_graphics::event::CGEventFlags::CGEventFlagControl) {
            modifiers |= 0x02; // Control
        }
        if flags.contains(core_graphics::event::CGEventFlags::CGEventFlagAlternate) {
            modifiers |= 0x04; // Alt/Option
        }
        if flags.contains(core_graphics::event::CGEventFlags::CGEventFlagCommand) {
            modifiers |= 0x08; // Command
        }

        Self {
            key_code,
            modifiers,
            timestamp: Instant::now(),
            event_type: KeyEventType::KeyDown,
        }
    }

    /// Convert macOS key code to character for Vietnamese processing
    #[cfg(target_os = "macos")]
    pub fn macos_key_to_char(&self) -> Option<char> {
        // macOS key code to character mapping for Vietnamese input
        match self.key_code {
            // Letters (QWERTY layout)
            0 => Some('a'), 1 => Some('s'), 2 => Some('d'), 3 => Some('f'), 4 => Some('h'),
            5 => Some('g'), 6 => Some('z'), 7 => Some('x'), 8 => Some('c'), 9 => Some('v'),
            11 => Some('b'), 12 => Some('q'), 13 => Some('w'), 14 => Some('e'), 15 => Some('r'),
            16 => Some('y'), 17 => Some('t'), 18 => Some('1'), 19 => Some('2'), 20 => Some('3'),
            21 => Some('4'), 22 => Some('6'), 23 => Some('5'), 24 => Some('='), 25 => Some('9'),
            26 => Some('7'), 27 => Some('-'), 28 => Some('8'), 29 => Some('0'), 30 => Some(']'),
            31 => Some('o'), 32 => Some('u'), 33 => Some('['), 34 => Some('i'), 35 => Some('p'),
            37 => Some('l'), 38 => Some('j'), 39 => Some('\''), 40 => Some('k'), 41 => Some(';'),
            42 => Some('\\'), 43 => Some(','), 44 => Some('/'), 45 => Some('n'), 46 => Some('m'),
            47 => Some('.'), 49 => Some(' '), // Space
            50 => Some('`'),

            // Special keys
            51 => Some('\u{0008}'), // Backspace
            117 => Some('\u{007F}'), // Delete

            _ => None,
        }
    }
}

/// Type of key event
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum KeyEventType {
    KeyDown,
    KeyUp,
    KeyRepeat,
}

/// Result of processing a keystroke
#[derive(Debug, Clone, PartialEq, Eq)]
pub enum ProcessResult {
    /// No change needed
    NoChange,
    /// Replace characters (backspace count, new characters)
    Replace {
        backspace_count: u8,
        new_chars: Vec<char>,
    },
    /// Replace with text
    ReplaceText {
        text: String,
        backspace_count: usize,
    },
    /// Append text
    Append {
        text: String,
    },
    /// Delete characters
    Delete {
        count: usize,
    },
    /// Replace and append
    ReplaceAndAppend {
        replace_text: String,
        append_text: String,
        backspace_count: usize,
    },
    /// Macro expansion (backspace count, expansion text)
    Macro {
        backspace_count: u8,
        expansion: String,
    },
    /// Restore previous state
    StateRestore {
        backspace_count: u8,
        restored_chars: Vec<char>,
    },
    /// Language mode toggle
    LanguageToggle { new_mode: super::LanguageMode },
    /// State change (internal)
    StateChange {
        description: String,
    },
    /// Error in processing
    Error {
        message: String,
    },
    /// Pass through original key
    PassThrough,
}

/// Input context for processing
#[derive(Debug, Clone)]
pub struct InputContext {
    pub current_state: InputState,
    pub settings: ProcessorSettings,
    pub performance_monitor: Arc<PerformanceMonitor>,
}

/// Settings for input processors
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProcessorSettings {
    pub spell_check_enabled: bool,
    pub modern_orthography: bool,
    pub quick_telex: bool,
    pub restore_if_wrong: bool,
    pub fix_browser_recommend: bool,
    pub auto_caps: bool,
}

impl Default for ProcessorSettings {
    fn default() -> Self {
        Self {
            spell_check_enabled: true,
            modern_orthography: true,
            quick_telex: false,
            restore_if_wrong: true,
            fix_browser_recommend: true,
            auto_caps: false,
        }
    }
}

/// Main Vietnamese input engine trait
pub trait VietnameseInputEngine: Send + Sync {
    /// Process a keystroke and return the result
    fn process_keystroke(&mut self, event: KeyEvent) -> Result<ProcessResult, EngineError>;

    /// Set the input method
    fn set_input_method(&mut self, method: super::InputMethod) -> Result<(), EngineError>;

    /// Set the encoding
    fn set_encoding(&mut self, encoding: super::Encoding) -> Result<(), EngineError>;

    /// Toggle language mode
    fn toggle_language_mode(&mut self) -> super::LanguageMode;

    /// Reset engine state
    fn reset_state(&mut self);

    /// Get current input state
    fn get_current_state(&self) -> &InputState;

    /// Restore previous state
    fn restore_previous_state(&mut self) -> Result<(), EngineError>;

    /// Get performance metrics
    fn get_performance_metrics(&self) -> crate::engine::performance::PerformanceMetrics;
}

/// Input method processor trait
pub trait InputMethodProcessor: Send + Sync {
    /// Get the name of this input method
    fn name(&self) -> &'static str;

    /// Process a key event
    fn process_key(&mut self, key: KeyEvent, context: &mut InputContext) -> ProcessResult;

    /// Process a character directly (for global capture integration)
    fn process_char(&mut self, ch: char) -> ProcessResult;

    /// Check if this processor can handle the given key
    fn can_handle_key(&self, key: &KeyEvent) -> bool;

    /// Reset processor state
    fn reset(&mut self);

    /// Reset processor state after successful text injection to prevent state corruption
    fn reset_after_injection(&mut self);

    /// Get processor configuration
    fn get_configuration(&self) -> ProcessorConfig;

    /// Set processor configuration
    fn set_configuration(&mut self, config: ProcessorConfig) -> Result<(), ConfigError>;

    /// Downcast to Any for accessing concrete type methods
    fn as_any_mut(&mut self) -> &mut dyn std::any::Any;
}

/// Configuration for input method processors
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProcessorConfig {
    pub enabled: bool,
    pub priority: u8,
    pub custom_mappings: std::collections::HashMap<String, String>,
}

impl Default for ProcessorConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            priority: 100,
            custom_mappings: std::collections::HashMap::new(),
        }
    }
}

/// Word boundary detection trait
pub trait WordBoundaryDetector: Send + Sync {
    /// Check if the given key represents a word boundary
    fn is_word_boundary(&self, key: &KeyEvent) -> bool;

    /// Check if the given key is a macro break code
    fn is_macro_break_code(&self, key: &KeyEvent) -> bool;

    /// Get list of word boundary characters
    fn get_boundary_chars(&self) -> &[u16];

    /// Get list of macro break characters
    fn get_macro_break_chars(&self) -> &[u16];
}

/// Character conversion trait
pub trait CharacterConverter: Send + Sync {
    /// Convert a key code to a character
    fn key_code_to_char(&self, key_code: u16) -> Option<char>;

    /// Convert a character to a key code
    fn char_to_key_code(&self, ch: char) -> Option<u16>;

    /// Get character with encoding
    fn get_character_with_encoding(
        &self,
        char_state: &CharacterState,
        encoding: super::Encoding,
    ) -> Option<char>;
}

/// Spell checking result
#[derive(Debug, Clone, PartialEq)]
pub enum SpellCheckResult {
    Valid,
    Invalid { suggestions: Vec<String> },
    Unknown,
}

/// Spell checking error types
#[derive(Debug, Clone)]
pub enum SpellError {
    DictionaryNotLoaded,
    InvalidWord,
    IoError(String),
}

/// Spell checker trait
pub trait SpellChecker: Send + Sync {
    fn check_word(&self, word: &str) -> SpellCheckResult;
    fn suggest_corrections(&self, word: &str) -> Vec<String>;
    fn add_to_dictionary(&mut self, word: String) -> Result<(), SpellError>;
    fn is_enabled(&self) -> bool;
    fn set_enabled(&mut self, enabled: bool);
    fn load_dictionary(&mut self, path: &Path) -> Result<(), SpellError>;
}

/// Error types for the engine
#[derive(Debug, Error)]
pub enum EngineError {
    #[error("Invalid input method: {method:?}")]
    InvalidInputMethod { method: super::InputMethod },

    #[error("Invalid encoding: {encoding:?}")]
    InvalidEncoding { encoding: super::Encoding },

    #[error("State error: {source}")]
    StateError {
        #[from]
        source: crate::engine::state::StateError,
    },

    #[error("Processing error: {message}")]
    ProcessingError { message: String },

    #[error("Configuration error: {source}")]
    ConfigError {
        #[from]
        source: ConfigError,
    },

    #[error("Performance threshold exceeded")]
    PerformanceThresholdExceeded,
}

/// Configuration errors
#[derive(Debug, Error)]
pub enum ConfigError {
    #[error("Invalid configuration value: {field}")]
    InvalidValue { field: String },

    #[error("Missing required configuration: {field}")]
    MissingRequired { field: String },

    #[error("Configuration validation failed: {message}")]
    ValidationFailed { message: String },
}

/// Event filter trait for performance optimization
pub trait EventFilter: Send + Sync {
    /// Get unique filter ID
    fn filter_id(&self) -> FilterId;

    /// Check if event should be processed
    fn should_process(&self, event: &KeyEvent) -> bool;

    /// Transform raw event to processed event
    fn transform(&self, event: KeyEvent) -> Option<KeyEvent>;

    /// Get filter priority (higher = processed first)
    fn priority(&self) -> FilterPriority;
}

/// Filter identifier
pub type FilterId = u32;

/// Filter priority
pub type FilterPriority = u8;

/// Standard filter priorities
pub mod filter_priorities {
    use super::FilterPriority;

    pub const HIGHEST: FilterPriority = 255;
    pub const HIGH: FilterPriority = 200;
    pub const NORMAL: FilterPriority = 100;
    pub const LOW: FilterPriority = 50;
    pub const LOWEST: FilterPriority = 1;
}

/// Macro for implementing basic event filter
#[macro_export]
macro_rules! impl_event_filter {
    ($struct_name:ident, $id:expr, $priority:expr) => {
        impl EventFilter for $struct_name {
            fn filter_id(&self) -> FilterId {
                $id
            }

            fn priority(&self) -> FilterPriority {
                $priority
            }

            fn should_process(&self, _event: &KeyEvent) -> bool {
                true
            }

            fn transform(&self, event: KeyEvent) -> Option<KeyEvent> {
                Some(event)
            }
        }
    };
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_key_event_creation() {
        let event = KeyEvent::new(b'a' as u16, 0);
        assert_eq!(event.key_code, b'a' as u16);
        assert_eq!(event.modifiers, 0);
        assert_eq!(event.event_type, KeyEventType::KeyDown);
    }

    #[test]
    fn test_process_result_variants() {
        let result = ProcessResult::NoChange;
        assert!(matches!(result, ProcessResult::NoChange));

        let result = ProcessResult::Replace {
            backspace_count: 1,
            new_chars: vec!['a'],
        };
        assert!(matches!(result, ProcessResult::Replace { .. }));
    }

    #[test]
    fn test_processor_settings() {
        let settings = ProcessorSettings::default();
        assert!(settings.spell_check_enabled);
        assert!(settings.modern_orthography);
        assert!(!settings.quick_telex);
    }
}
