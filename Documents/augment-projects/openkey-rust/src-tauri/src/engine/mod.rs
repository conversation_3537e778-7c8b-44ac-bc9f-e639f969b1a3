// Core engine modules
pub mod boundary;
pub mod character_encoding_converter;
pub mod encoding;
pub mod keyboard_event_processor;
pub mod performance;
pub mod spell_check;
pub mod state;
pub mod telex;
pub mod traits;
pub mod vietnamese;
pub mod vni;

// Enhanced Vietnamese processing modules
pub mod vietnamese_text_validator;
pub mod word_boundary_detector;
pub mod context_aware_w_processor;
pub mod vietnamese_state_machine;
pub mod integrated_vietnamese_processor;

#[cfg(test)]
pub mod vietnamese_telex_tests;

#[cfg(test)]
pub mod tests;

#[cfg(test)]
pub mod integrated_processor_tests;

// Re-export core types
pub use character_encoding_converter::*;
pub use encoding::*;
pub use keyboard_event_processor::*;
pub use performance::*;
pub use state::{CharacterFlags, CharacterState, InputState, MarkType, ToneType as StateToneType};
pub use telex::{TelexConfig, TelexProcessor, TelexVowelProcessor, ToneType as TelexToneType};
pub use traits::*;
pub use vietnamese::*;
pub use vni::*;

use crate::features::macro_system::{MacroExpansion, MacroSystem};
use serde::{Deserialize, Serialize};

#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum InputMethod {
    Telex = 0,
    VNI = 1,
    SimpleTelex1 = 2,
    SimpleTelex2 = 3,
}

#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum Encoding {
    Unicode = 0,
    Tcvn3 = 1,
    VniWindows = 2,
    UnicodeCompound = 3,
    VietnameseLocale = 4,
}

#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum LanguageMode {
    English = 0,
    Vietnamese = 1,
}

/// Main Vietnamese input engine with performance optimization
pub struct VietnameseEngine {
    // Core configuration
    pub language_mode: LanguageMode,
    pub input_method: InputMethod,
    pub encoding: Encoding,

    // Input state management
    pub input_state: InputState,

    // Feature flags
    pub spell_check_enabled: bool,
    pub modern_orthography: bool,
    pub quick_telex: bool,
    pub restore_if_wrong: bool,
    pub fix_browser_recommend: bool,

    // Performance monitoring
    pub performance_monitor: std::sync::Arc<PerformanceMonitor>,

    // Input method processors
    telex_processor: Option<Box<dyn InputMethodProcessor>>,
    vni_processor: Option<Box<dyn InputMethodProcessor>>,
    simple_telex_processor: Option<Box<dyn InputMethodProcessor>>,

    // Macro system
    macro_system: MacroSystem,
}

impl Default for VietnameseEngine {
    fn default() -> Self {
        Self {
            language_mode: LanguageMode::Vietnamese,
            input_method: InputMethod::Telex,
            encoding: Encoding::Unicode,
            input_state: InputState::new(),
            spell_check_enabled: true,
            modern_orthography: true,
            quick_telex: false,
            restore_if_wrong: true,
            fix_browser_recommend: true,
            performance_monitor: std::sync::Arc::new(PerformanceMonitor::new()),
            telex_processor: None,
            vni_processor: None,
            simple_telex_processor: None,
            macro_system: MacroSystem::new(),
        }
    }
}

impl VietnameseEngine {
    pub fn new() -> Self {
        Self::default()
    }

    pub fn process_key(&mut self, key_code: u16, modifiers: u8) -> Option<String> {
        match self.language_mode {
            LanguageMode::English => None,
            LanguageMode::Vietnamese => match self.input_method {
                InputMethod::Telex => self.process_telex_key(key_code, modifiers),
                InputMethod::VNI => self.process_vni_key(key_code, modifiers),
                InputMethod::SimpleTelex1 | InputMethod::SimpleTelex2 => {
                    self.process_simple_telex_key(key_code, modifiers)
                }
            },
        }
    }

    pub fn toggle_language_mode(&mut self) -> LanguageMode {
        self.language_mode = match self.language_mode {
            LanguageMode::English => LanguageMode::Vietnamese,
            LanguageMode::Vietnamese => LanguageMode::English,
        };
        self.language_mode
    }

    pub fn set_input_method(&mut self, method: InputMethod) {
        self.input_method = method;
    }

    pub fn set_encoding(&mut self, encoding: Encoding) {
        self.encoding = encoding;
    }

    /// Reset engine state after successful text injection to prevent state corruption
    pub fn reset_after_injection(&mut self) {
        // Reset all processor states to prevent corruption from injected events
        if let Some(ref mut processor) = self.telex_processor {
            processor.reset_after_injection();
        }
        if let Some(ref mut processor) = self.vni_processor {
            processor.reset_after_injection();
        }
        if let Some(ref mut processor) = self.simple_telex_processor {
            processor.reset_after_injection();
        }

        // Reset input state
        self.input_state.reset();

        log::debug!("🔄 VietnameseEngine state reset after injection");
    }

    pub fn start_new_session(&mut self) {
        self.input_state.reset();
    }

    /// Convert key code to character for processing
    fn key_code_to_char(&self, key_code: u16) -> Option<char> {
        // Basic ASCII key code to character mapping
        match key_code {
            32..=126 => Some(key_code as u8 as char), // Printable ASCII
            _ => None,
        }
    }

    fn process_telex_key(&mut self, key_code: u16, modifiers: u8) -> Option<String> {
        // Convert key code to character
        if let Some(_ch) = self.key_code_to_char(key_code) {
            // Create Vietnamese processor if not exists
            if self.telex_processor.is_none() {
                let mut processor = vietnamese::VietnameseProcessor::new();
                processor.input_method = InputMethod::Telex;
                processor.restore_if_wrong = self.restore_if_wrong;
                self.telex_processor = Some(Box::new(processor));
            }

            // Process with Telex processor
            if let Some(processor) = &mut self.telex_processor {
                let key_event = traits::KeyEvent::new(key_code, modifiers);
                let mut context = traits::InputContext {
                    current_state: self.input_state.clone(),
                    settings: traits::ProcessorSettings::default(),
                    performance_monitor: std::sync::Arc::new(performance::PerformanceMonitor::new()),
                };

                match processor.process_key(key_event, &mut context) {
                    traits::ProcessResult::Replace {
                        backspace_count,
                        new_chars,
                    } => {
                        // Update input state
                        self.input_state = context.current_state;

                        // Create output string with backspaces and new characters
                        let mut result = String::new();
                        for _ in 0..backspace_count {
                            result.push('\u{0008}'); // Backspace character
                        }
                        result.extend(new_chars);
                        Some(result)
                    }
                    traits::ProcessResult::NoChange => None,
                    traits::ProcessResult::Macro {
                        backspace_count,
                        expansion,
                    } => {
                        // Handle macro expansion
                        let mut result = String::new();
                        for _ in 0..backspace_count {
                            result.push('\u{0008}'); // Backspace to remove trigger
                        }
                        result.push_str(&expansion);
                        Some(result)
                    }
                    traits::ProcessResult::StateRestore { backspace_count, restored_chars } => {
                        // Handle state restoration
                        log::debug!("🔄 [STATE_RESTORE] Restoring {} characters with {} backspaces", restored_chars.len(), backspace_count);

                        // Restore the previous text state
                        if let Err(e) = self.input_state.restore_previous_state() {
                            log::warn!("⚠️  [STATE_RESTORE] Failed to restore state: {}", e);
                        }

                        // Return the restored text
                        Some(restored_chars.iter().collect())
                    }
                    traits::ProcessResult::LanguageToggle { new_mode } => {
                        // Handle language toggle
                        log::debug!("🌐 [LANGUAGE_TOGGLE] Toggling to mode: {:?}", new_mode);

                        // Update language mode
                        self.language_mode = new_mode;

                        // Reset processors when switching languages
                        if let Some(ref mut processor) = self.telex_processor {
                            processor.reset();
                        }
                        if let Some(ref mut processor) = self.vni_processor {
                            processor.reset();
                        }
                        if let Some(ref mut processor) = self.simple_telex_processor {
                            processor.reset();
                        }

                        // Return language mode indicator
                        let mode_text = match new_mode {
                            LanguageMode::Vietnamese => "🇻🇳 Vietnamese",
                            LanguageMode::English => "🇺🇸 English",
                        };

                        Some(mode_text.to_string())
                    }
                    // Handle new ProcessResult variants
                    traits::ProcessResult::ReplaceText { text, backspace_count } => {
                        let mut result = String::new();
                        for _ in 0..backspace_count {
                            result.push('\u{0008}');
                        }
                        result.push_str(&text);
                        Some(result)
                    }
                    traits::ProcessResult::Append { text } => Some(text),
                    traits::ProcessResult::Delete { count } => {
                        let mut result = String::new();
                        for _ in 0..count {
                            result.push('\u{0008}');
                        }
                        Some(result)
                    }
                    traits::ProcessResult::ReplaceAndAppend { replace_text, append_text, backspace_count } => {
                        let mut result = String::new();
                        for _ in 0..backspace_count {
                            result.push('\u{0008}');
                        }
                        result.push_str(&replace_text);
                        result.push_str(&append_text);
                        Some(result)
                    }
                    traits::ProcessResult::StateChange { .. } => None,
                    traits::ProcessResult::Error { .. } => None,
                    traits::ProcessResult::PassThrough => None,
                }
            } else {
                None
            }
        } else {
            None
        }
    }

    fn process_vni_key(&mut self, key_code: u16, modifiers: u8) -> Option<String> {
        // Convert key code to character
        if let Some(_ch) = self.key_code_to_char(key_code) {
            // Create Vietnamese processor if not exists
            if self.vni_processor.is_none() {
                let mut processor = vietnamese::VietnameseProcessor::new();
                processor.input_method = InputMethod::VNI;
                processor.restore_if_wrong = self.restore_if_wrong;
                self.vni_processor = Some(Box::new(processor));
            }

            // Process with VNI processor
            if let Some(processor) = &mut self.vni_processor {
                let key_event = traits::KeyEvent::new(key_code, modifiers);
                let mut context = traits::InputContext {
                    current_state: self.input_state.clone(),
                    settings: traits::ProcessorSettings::default(),
                    performance_monitor: std::sync::Arc::new(performance::PerformanceMonitor::new()),
                };

                match processor.process_key(key_event, &mut context) {
                    traits::ProcessResult::Replace {
                        backspace_count,
                        new_chars,
                    } => {
                        self.input_state = context.current_state;
                        let mut result = String::new();
                        for _ in 0..backspace_count {
                            result.push('\u{0008}');
                        }
                        result.extend(new_chars);
                        Some(result)
                    }
                    traits::ProcessResult::NoChange => None,
                    traits::ProcessResult::Macro {
                        backspace_count,
                        expansion,
                    } => {
                        let mut result = String::new();
                        for _ in 0..backspace_count {
                            result.push('\u{0008}');
                        }
                        result.push_str(&expansion);
                        Some(result)
                    }
                    traits::ProcessResult::StateRestore { .. } => None,
                    traits::ProcessResult::LanguageToggle { .. } => None,
                    // Handle new ProcessResult variants
                    traits::ProcessResult::ReplaceText { text, backspace_count } => {
                        let mut result = String::new();
                        for _ in 0..backspace_count {
                            result.push('\u{0008}');
                        }
                        result.push_str(&text);
                        Some(result)
                    }
                    traits::ProcessResult::Append { text } => Some(text),
                    traits::ProcessResult::Delete { count } => {
                        let mut result = String::new();
                        for _ in 0..count {
                            result.push('\u{0008}');
                        }
                        Some(result)
                    }
                    traits::ProcessResult::ReplaceAndAppend { replace_text, append_text, backspace_count } => {
                        let mut result = String::new();
                        for _ in 0..backspace_count {
                            result.push('\u{0008}');
                        }
                        result.push_str(&replace_text);
                        result.push_str(&append_text);
                        Some(result)
                    }
                    traits::ProcessResult::StateChange { .. } => None,
                    traits::ProcessResult::Error { .. } => None,
                    traits::ProcessResult::PassThrough => None,
                }
            } else {
                None
            }
        } else {
            None
        }
    }

    fn process_simple_telex_key(&mut self, key_code: u16, modifiers: u8) -> Option<String> {
        // Convert key code to character
        if let Some(_ch) = self.key_code_to_char(key_code) {
            // Create Vietnamese processor if not exists
            if self.simple_telex_processor.is_none() {
                let mut processor = vietnamese::VietnameseProcessor::new();
                processor.input_method = match self.input_method {
                    InputMethod::SimpleTelex1 => InputMethod::SimpleTelex1,
                    InputMethod::SimpleTelex2 => InputMethod::SimpleTelex2,
                    _ => InputMethod::SimpleTelex1, // Default fallback
                };
                processor.restore_if_wrong = self.restore_if_wrong;
                self.simple_telex_processor = Some(Box::new(processor));
            }

            // Process with Simple Telex processor
            if let Some(processor) = &mut self.simple_telex_processor {
                let key_event = traits::KeyEvent::new(key_code, modifiers);
                let mut context = traits::InputContext {
                    current_state: self.input_state.clone(),
                    settings: traits::ProcessorSettings::default(),
                    performance_monitor: std::sync::Arc::new(performance::PerformanceMonitor::new()),
                };

                match processor.process_key(key_event, &mut context) {
                    traits::ProcessResult::Replace {
                        backspace_count,
                        new_chars,
                    } => {
                        self.input_state = context.current_state;
                        let mut result = String::new();
                        for _ in 0..backspace_count {
                            result.push('\u{0008}');
                        }
                        result.extend(new_chars);
                        Some(result)
                    }
                    traits::ProcessResult::NoChange => None,
                    traits::ProcessResult::Macro {
                        backspace_count,
                        expansion,
                    } => {
                        let mut result = String::new();
                        for _ in 0..backspace_count {
                            result.push('\u{0008}');
                        }
                        result.push_str(&expansion);
                        Some(result)
                    }
                    traits::ProcessResult::StateRestore { .. } => None,
                    traits::ProcessResult::LanguageToggle { .. } => None,
                    // Handle new ProcessResult variants
                    traits::ProcessResult::ReplaceText { text, backspace_count } => {
                        let mut result = String::new();
                        for _ in 0..backspace_count {
                            result.push('\u{0008}');
                        }
                        result.push_str(&text);
                        Some(result)
                    }
                    traits::ProcessResult::Append { text } => Some(text),
                    traits::ProcessResult::Delete { count } => {
                        let mut result = String::new();
                        for _ in 0..count {
                            result.push('\u{0008}');
                        }
                        Some(result)
                    }
                    traits::ProcessResult::ReplaceAndAppend { replace_text, append_text, backspace_count } => {
                        let mut result = String::new();
                        for _ in 0..backspace_count {
                            result.push('\u{0008}');
                        }
                        result.push_str(&replace_text);
                        result.push_str(&append_text);
                        Some(result)
                    }
                    traits::ProcessResult::StateChange { .. } => None,
                    traits::ProcessResult::Error { .. } => None,
                    traits::ProcessResult::PassThrough => None,
                }
            } else {
                None
            }
        } else {
            None
        }
    }

    /// Get mutable reference to macro system
    pub fn macro_system_mut(&mut self) -> &mut MacroSystem {
        &mut self.macro_system
    }

    /// Get reference to macro system
    pub fn macro_system(&self) -> &MacroSystem {
        &self.macro_system
    }

    /// Process macro expansion for given key event
    pub fn process_macro(
        &mut self,
        key_event: &traits::KeyEvent,
        is_break_char: bool,
    ) -> Option<MacroExpansion> {
        self.macro_system
            .process_key_event(key_event, is_break_char)
    }

    /// Check if character is a word break character for macro processing
    pub fn is_macro_break_char(&self, ch: char) -> bool {
        matches!(
            ch,
            ' ' | '\t'
                | '\n'
                | '.'
                | ','
                | ';'
                | ':'
                | '!'
                | '?'
                | '"'
                | '\''
                | '('
                | ')'
                | '['
                | ']'
                | '{'
                | '}'
        )
    }

    /// Reset macro buffer (called on word boundaries)
    pub fn reset_macro_buffer(&mut self) {
        self.macro_system.reset_buffer();
    }

    /// Process a sequence of keys (for testing purposes)
    pub fn process_key_sequence(&mut self, sequence: &str) -> String {
        let mut result = String::new();

        for ch in sequence.chars() {
            let key_event = KeyEvent::new(ch as u16, 0);
            match self.process_key_event(key_event) {
                ProcessResult::Replace { backspace_count: _, new_chars } => {
                    result.extend(new_chars);
                },
                ProcessResult::Append { text } => {
                    result.push_str(&text);
                },
                ProcessResult::Macro { backspace_count: _, expansion } => {
                    result.push_str(&expansion);
                },
                _ => {
                    // For other results, just add the original character
                    result.push(ch);
                }
            }
        }

        result
    }



    /// Reset the engine state
    pub fn reset(&mut self) {
        self.input_state.reset();

        // Reset all processors
        if let Some(ref mut processor) = self.telex_processor {
            processor.reset();
        }
        if let Some(ref mut processor) = self.vni_processor {
            processor.reset();
        }
        if let Some(ref mut processor) = self.simple_telex_processor {
            processor.reset();
        }

        log::debug!("Vietnamese engine reset completed");
    }

    /// Check if Vietnamese input mode is active
    pub fn is_vietnamese_mode(&self) -> bool {
        matches!(self.language_mode, LanguageMode::Vietnamese)
    }

    /// Get the current processor for state management
    pub fn get_current_processor_mut(&mut self) -> Option<&mut Box<dyn InputMethodProcessor>> {
        match self.input_method {
            InputMethod::Telex => self.telex_processor.as_mut(),
            InputMethod::VNI => self.vni_processor.as_mut(),
            InputMethod::SimpleTelex1 | InputMethod::SimpleTelex2 => self.simple_telex_processor.as_mut(),
        }
    }

    /// Handle backspace by removing one character from the Vietnamese processor buffer
    pub fn handle_processor_backspace(&mut self) {
        match self.input_method {
            InputMethod::Telex => {
                if let Some(ref mut processor) = self.telex_processor {
                    // We need to downcast to access the specific VietnameseProcessor methods
                    // For now, we'll use a different approach - process a backspace key event
                    let backspace_event = KeyEvent::new(8, 0); // ASCII backspace
                    let mut context = traits::InputContext {
                        current_state: self.input_state.clone(),
                        settings: traits::ProcessorSettings::default(),
                        performance_monitor: std::sync::Arc::new(performance::PerformanceMonitor::new()),
                    };
                    let _result = processor.process_key(backspace_event, &mut context);
                    self.input_state = context.current_state;
                }
            }
            InputMethod::VNI => {
                if let Some(ref mut processor) = self.vni_processor {
                    let backspace_event = KeyEvent::new(8, 0);
                    let mut context = traits::InputContext {
                        current_state: self.input_state.clone(),
                        settings: traits::ProcessorSettings::default(),
                        performance_monitor: std::sync::Arc::new(performance::PerformanceMonitor::new()),
                    };
                    let _result = processor.process_key(backspace_event, &mut context);
                    self.input_state = context.current_state;
                }
            }
            InputMethod::SimpleTelex1 | InputMethod::SimpleTelex2 => {
                if let Some(ref mut processor) = self.simple_telex_processor {
                    let backspace_event = KeyEvent::new(8, 0);
                    let mut context = traits::InputContext {
                        current_state: self.input_state.clone(),
                        settings: traits::ProcessorSettings::default(),
                        performance_monitor: std::sync::Arc::new(performance::PerformanceMonitor::new()),
                    };
                    let _result = processor.process_key(backspace_event, &mut context);
                    self.input_state = context.current_state;
                }
            }
        }
    }

    /// Process a character input
    pub fn process_character(&mut self, ch: char) -> ProcessResult {
        // Process using the existing engine logic
        match self.input_method {
            InputMethod::Telex => {
                // Initialize Telex processor if not exists
                if self.telex_processor.is_none() {
                    let mut processor = vietnamese::VietnameseProcessor::new();
                    processor.input_method = InputMethod::Telex;
                    processor.restore_if_wrong = self.restore_if_wrong;
                    processor.quick_telex_enabled = self.quick_telex;
                    self.telex_processor = Some(Box::new(processor));
                }

                if let Some(ref mut processor) = self.telex_processor {
                    let mut context = traits::InputContext {
                        current_state: self.input_state.clone(),
                        settings: traits::ProcessorSettings::default(),
                        performance_monitor: std::sync::Arc::new(performance::PerformanceMonitor::new()),
                    };
                    // Process the character directly instead of creating a KeyEvent
                    let result = processor.process_char(ch);
                    self.input_state = context.current_state;
                    result
                } else {
                    ProcessResult::PassThrough
                }
            }
            InputMethod::VNI => {
                // Initialize VNI processor if not exists
                if self.vni_processor.is_none() {
                    let mut processor = vietnamese::VietnameseProcessor::new();
                    processor.input_method = InputMethod::VNI;
                    processor.restore_if_wrong = self.restore_if_wrong;
                    self.vni_processor = Some(Box::new(processor));
                }

                if let Some(ref mut processor) = self.vni_processor {
                    let mut context = traits::InputContext {
                        current_state: self.input_state.clone(),
                        settings: traits::ProcessorSettings::default(),
                        performance_monitor: std::sync::Arc::new(performance::PerformanceMonitor::new()),
                    };
                    // Process the character directly instead of creating a KeyEvent
                    let result = processor.process_char(ch);
                    self.input_state = context.current_state;
                    result
                } else {
                    ProcessResult::PassThrough
                }
            }
            InputMethod::SimpleTelex1 | InputMethod::SimpleTelex2 => {
                // Initialize Simple Telex processor if not exists
                if self.simple_telex_processor.is_none() {
                    let mut processor = vietnamese::VietnameseProcessor::new();
                    processor.input_method = match self.input_method {
                        InputMethod::SimpleTelex1 => InputMethod::SimpleTelex1,
                        InputMethod::SimpleTelex2 => InputMethod::SimpleTelex2,
                        _ => InputMethod::SimpleTelex1, // Default fallback
                    };
                    processor.restore_if_wrong = self.restore_if_wrong;
                    self.simple_telex_processor = Some(Box::new(processor));
                }

                if let Some(ref mut processor) = self.simple_telex_processor {
                    let mut context = traits::InputContext {
                        current_state: self.input_state.clone(),
                        settings: traits::ProcessorSettings::default(),
                        performance_monitor: std::sync::Arc::new(performance::PerformanceMonitor::new()),
                    };
                    // Process the character directly instead of creating a KeyEvent
                    let result = processor.process_char(ch);
                    self.input_state = context.current_state;
                    result
                } else {
                    ProcessResult::PassThrough
                }
            }
        }
    }

    /// Process a key event (wrapper for global capture integration)
    pub fn process_key_event(&mut self, event: KeyEvent) -> ProcessResult {
        // Convert KeyEvent to character and process
        if let Some(ch) = event.to_char() {
            self.process_character(ch)
        } else {
            // Handle special keys
            if event.is_backspace() {
                self.handle_backspace()
            } else if event.is_delete() {
                self.handle_delete()
            } else {
                ProcessResult::PassThrough
            }
        }
    }

    /// Handle backspace key
    fn handle_backspace(&mut self) -> ProcessResult {
        log::debug!("⌫ [BACKSPACE] Processing backspace key");

        // Try to restore previous state if we have Vietnamese text in buffer
        match self.input_state.restore_previous_state() {
            Ok(()) => {
                log::debug!("✅ [BACKSPACE] Successfully restored previous state");

                // Get the current buffer content after restoration
                let current_content = self.get_current_buffer_content();

                if !current_content.is_empty() {
                    // Return the restored content
                    ProcessResult::ReplaceText {
                        text: current_content,
                        backspace_count: 1, // Remove the character that triggered backspace
                    }
                } else {
                    // Buffer is empty after restoration, just pass through the backspace
                    ProcessResult::PassThrough
                }
            }
            Err(_) => {
                // No previous state to restore, pass through the backspace
                log::debug!("⌫ [BACKSPACE] No previous state to restore, passing through");
                ProcessResult::PassThrough
            }
        }
    }

    /// Handle delete key
    fn handle_delete(&mut self) -> ProcessResult {
        log::debug!("⌦ [DELETE] Processing delete key");

        // For delete key, we typically just pass it through since it affects
        // characters after the cursor, not the Vietnamese input buffer
        ProcessResult::PassThrough
    }

    /// Get current buffer content for state restoration
    fn get_current_buffer_content(&self) -> String {
        // Get content from the active input method processor
        match self.input_method {
            InputMethod::Telex | InputMethod::SimpleTelex1 | InputMethod::SimpleTelex2 => {
                if let Some(_processor) = &self.telex_processor {
                    // For now, return empty as we need to access the Vietnamese processor
                    // In a full implementation, we'd have a way to get buffer content
                    String::new()
                } else {
                    String::new()
                }
            }
            InputMethod::VNI => {
                if let Some(_processor) = &self.vni_processor {
                    // Similar for VNI processor
                    String::new()
                } else {
                    String::new()
                }
            }
        }
    }

    /// Handle backspace with Vietnamese processor integration
    pub fn handle_backspace_with_processor(&mut self) -> ProcessResult {
        log::debug!("⌫ [BACKSPACE_PROCESSOR] Processing backspace with Vietnamese integration");

        // For now, just pass through the backspace
        // In a full implementation, we'd integrate with the Vietnamese processor
        // to handle proper undo functionality
        ProcessResult::PassThrough
    }

    /// Handle backspace by directly calling the Vietnamese processor's handle_backspace method
    pub fn handle_vietnamese_backspace(&mut self) {
        log::debug!("🔄 [VIETNAMESE_BACKSPACE] Starting Vietnamese backspace handling");
        match self.input_method {
            InputMethod::Telex => {
                if let Some(ref mut processor) = self.telex_processor {
                    // Try to downcast to VietnameseProcessor to call handle_backspace directly
                    if let Some(vietnamese_processor) = processor.as_any_mut().downcast_mut::<crate::engine::vietnamese::VietnameseProcessor>() {
                        let result = vietnamese_processor.handle_backspace();
                        log::debug!("✅ [VIETNAMESE_BACKSPACE] Called Vietnamese processor handle_backspace, result: {:?}", result);
                    } else {
                        log::debug!("⚠️ [VIETNAMESE_BACKSPACE] Failed to downcast to VietnameseProcessor");
                    }
                }
            }
            _ => {
                log::debug!("⚠️ [VIETNAMESE_BACKSPACE] Not using Telex input method");
            }
        }
    }
}
