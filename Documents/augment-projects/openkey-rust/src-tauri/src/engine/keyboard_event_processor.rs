use crate::engine::{VietnameseEngine, PerformanceMonitor};
use crate::engine::traits::{KeyEvent, ProcessResult};
use crate::platform::{PlatformError, TextInjector, ApplicationContextManager, ApplicationInfo};
use std::sync::{<PERSON>, Mutex};
use log;

/// Keyboard event processor for handling global keyboard capture
pub struct KeyboardEventProcessor {
    /// Vietnamese input engine
    vietnamese_engine: Arc<Mutex<VietnameseEngine>>,
    
    /// Performance monitoring
    performance_monitor: Arc<PerformanceMonitor>,
    
    /// Text injection system
    text_injector: Arc<dyn TextInjector>,
    
    /// Application context manager
    app_context: Arc<dyn ApplicationContextManager>,
    
    /// Current application info
    current_app: Option<ApplicationInfo>,
    
    /// Processing statistics
    events_processed: std::sync::atomic::AtomicU64,
    events_injected: std::sync::atomic::AtomicU64,
}

impl KeyboardEventProcessor {
    /// Create new keyboard event processor
    pub fn new(
        vietnamese_engine: Arc<Mutex<VietnameseEngine>>,
        performance_monitor: Arc<PerformanceMonitor>,
        text_injector: Arc<dyn TextInjector>,
        app_context: Arc<dyn ApplicationContextManager>,
    ) -> Self {
        Self {
            vietnamese_engine,
            performance_monitor,
            text_injector,
            app_context,
            current_app: None,
            events_processed: std::sync::atomic::AtomicU64::new(0),
            events_injected: std::sync::atomic::AtomicU64::new(0),
        }
    }
    
    /// Process a keyboard event
    pub fn process_event(&mut self, event: KeyEvent) -> Result<bool, PlatformError> {
        let start_time = std::time::Instant::now();
        
        // Update application context
        self.update_application_context()?;
        
        // Increment event counter
        self.events_processed.fetch_add(1, std::sync::atomic::Ordering::Relaxed);
        
        // Process the event with Vietnamese engine
        let result = {
            let mut engine = self.vietnamese_engine.lock().unwrap();
            engine.process_key_event(event)
        };
        
        // Handle the processing result
        let handled = self.handle_process_result(result)?;
        
        // Record processing time
        let processing_time = start_time.elapsed();
        self.performance_monitor.record_processing_time(processing_time);
        
        log::debug!(
            "Event processed in {:?}, handled: {}, total events: {}",
            processing_time,
            handled,
            self.events_processed.load(std::sync::atomic::Ordering::Relaxed)
        );
        
        Ok(handled)
    }
    
    /// Update application context
    fn update_application_context(&mut self) -> Result<(), PlatformError> {
        match self.app_context.get_active_application() {
            Ok(app_info) => {
                // Check if application changed
                let app_changed = self.current_app.as_ref()
                    .map(|current| current.identifier != app_info.identifier)
                    .unwrap_or(true);
                
                if app_changed {
                    log::debug!("Application changed to: {}", app_info.name);
                    self.current_app = Some(app_info);
                    
                    // Reset Vietnamese engine state on app change
                    if let Ok(mut engine) = self.vietnamese_engine.lock() {
                        engine.reset();
                    }
                }
                Ok(())
            }
            Err(e) => {
                log::warn!("Failed to get active application: {}", e);
                Err(e)
            }
        }
    }
    
    /// Handle processing result from Vietnamese engine
    fn handle_process_result(&mut self, result: ProcessResult) -> Result<bool, PlatformError> {
        match result {
            ProcessResult::NoChange => {
                // Event was processed but no output needed
                Ok(true)
            }

            ProcessResult::Replace { new_chars, backspace_count } => {
                let text: String = new_chars.iter().collect();
                println!("💉 INJECT: bs:{} + '{}'", backspace_count, text);

                // Send backspaces first, then new characters
                if backspace_count > 0 {
                    self.inject_backspaces(backspace_count as usize)?;
                }

                self.inject_text(&text)?;

                // CRITICAL: Reset Vietnamese engine state after successful injection
                {
                    let mut engine = self.vietnamese_engine.lock().unwrap();
                    engine.reset_after_injection();
                }

                Ok(true)
            }

            ProcessResult::ReplaceText { text, backspace_count } => {
                // Send backspaces first, then replacement text
                if backspace_count > 0 {
                    self.inject_backspaces(backspace_count)?;
                }
                self.inject_text(&text)?;
                Ok(true)
            }

            ProcessResult::Append { text } => {
                // Just append the text
                self.inject_text(&text)?;
                Ok(true)
            }

            ProcessResult::Delete { count } => {
                // Send delete characters
                self.inject_backspaces(count)?;
                Ok(true)
            }

            ProcessResult::ReplaceAndAppend { replace_text, append_text, backspace_count } => {
                // Send backspaces, then replace text, then append text
                if backspace_count > 0 {
                    self.inject_backspaces(backspace_count)?;
                }
                self.inject_text(&replace_text)?;
                self.inject_text(&append_text)?;
                Ok(true)
            }

            ProcessResult::Macro { backspace_count, expansion } => {
                // Handle macro expansion
                if backspace_count > 0 {
                    self.inject_backspaces(backspace_count as usize)?;
                }
                self.inject_text(&expansion)?;
                Ok(true)
            }

            ProcessResult::StateRestore { backspace_count, restored_chars } => {
                // Handle state restoration
                if backspace_count > 0 {
                    self.inject_backspaces(backspace_count as usize)?;
                }
                let text: String = restored_chars.into_iter().collect();
                self.inject_text(&text)?;
                Ok(true)
            }

            ProcessResult::LanguageToggle { new_mode } => {
                // Handle language toggle - could show notification
                log::info!("Language toggled to: {:?}", new_mode);
                Ok(true)
            }

            ProcessResult::StateChange { description } => {
                // Handle state change
                log::debug!("State change: {}", description);
                Ok(true)
            }

            ProcessResult::Error { message } => {
                // Handle error
                log::error!("Processing error: {}", message);
                Ok(false)
            }

            ProcessResult::PassThrough => {
                // Let the original key event pass through
                Ok(false)
            }
        }
    }
    
    /// Inject text into the active application
    fn inject_text(&mut self, text: &str) -> Result<(), PlatformError> {
        if !text.is_empty() {
            self.text_injector.inject_text(text)?;
            self.events_injected.fetch_add(1, std::sync::atomic::Ordering::Relaxed);
            log::debug!("Injected text: '{}'", text);
        }
        Ok(())
    }
    
    /// Inject backspace characters
    fn inject_backspaces(&mut self, count: usize) -> Result<(), PlatformError> {
        if count > 0 {
            // Create backspace string
            let backspaces = "\u{0008}".repeat(count);
            self.text_injector.inject_text(&backspaces)?;
            self.events_injected.fetch_add(count as u64, std::sync::atomic::Ordering::Relaxed);
            log::debug!("Injected {} backspaces", count);
        }
        Ok(())
    }
    
    /// Get processing statistics
    pub fn get_statistics(&self) -> (u64, u64) {
        (
            self.events_processed.load(std::sync::atomic::Ordering::Relaxed),
            self.events_injected.load(std::sync::atomic::Ordering::Relaxed),
        )
    }
    
    /// Reset statistics
    pub fn reset_statistics(&mut self) {
        self.events_processed.store(0, std::sync::atomic::Ordering::Relaxed);
        self.events_injected.store(0, std::sync::atomic::Ordering::Relaxed);
    }
    
    /// Get current application info
    pub fn get_current_application(&self) -> Option<&ApplicationInfo> {
        self.current_app.as_ref()
    }
}

// Legacy alias for backward compatibility
pub type EventProcessor = KeyboardEventProcessor;

#[cfg(test)]
mod tests {
    use super::*;
    use crate::platform::traits::{InjectionMethod, AppSettings};

    /// Mock text injector for testing
    struct MockTextInjector {
        injected_text: Arc<Mutex<Vec<String>>>,
    }

    impl MockTextInjector {
        fn new() -> Self {
            Self {
                injected_text: Arc::new(Mutex::new(Vec::new())),
            }
        }

        fn get_injected_text(&self) -> Vec<String> {
            self.injected_text.lock().unwrap().clone()
        }
    }

    impl TextInjector for MockTextInjector {
        fn inject_text(&self, text: &str) -> Result<(), PlatformError> {
            self.injected_text.lock().unwrap().push(text.to_string());
            Ok(())
        }

        fn inject_text_with_method(&self, text: &str, _method: InjectionMethod) -> Result<(), PlatformError> {
            self.inject_text(text)
        }

        fn get_available_methods(&self) -> Vec<InjectionMethod> {
            vec![InjectionMethod::Native]
        }

        fn get_recommended_method(&self, _app_info: &ApplicationInfo) -> InjectionMethod {
            InjectionMethod::Native
        }
    }

    /// Mock application context manager for testing
    struct MockApplicationContextManager {
        current_app: ApplicationInfo,
    }

    impl MockApplicationContextManager {
        fn new() -> Self {
            Self {
                current_app: ApplicationInfo {
                    identifier: "test.app".to_string(),
                    name: "Test App".to_string(),
                    pid: 1234,
                    app_type: crate::platform::traits::ApplicationType::Native,
                },
            }
        }
    }

    impl ApplicationContextManager for MockApplicationContextManager {
        fn get_active_application(&self) -> Result<ApplicationInfo, PlatformError> {
            Ok(self.current_app.clone())
        }

        fn get_app_settings(&self, _app_id: &str) -> Result<AppSettings, PlatformError> {
            Ok(AppSettings::default())
        }

        fn save_app_settings(&self, _app_id: &str, _settings: &AppSettings) -> Result<(), PlatformError> {
            Ok(())
        }
    }

    #[test]
    fn test_keyboard_event_processor_creation() {
        let engine = Arc::new(Mutex::new(VietnameseEngine::new()));
        let performance_monitor = Arc::new(PerformanceMonitor::new());
        let text_injector = Arc::new(MockTextInjector::new());
        let app_context = Arc::new(MockApplicationContextManager::new());

        let processor = KeyboardEventProcessor::new(
            engine,
            performance_monitor,
            text_injector,
            app_context,
        );

        let (events_processed, events_injected) = processor.get_statistics();
        assert_eq!(events_processed, 0);
        assert_eq!(events_injected, 0);
    }
}
