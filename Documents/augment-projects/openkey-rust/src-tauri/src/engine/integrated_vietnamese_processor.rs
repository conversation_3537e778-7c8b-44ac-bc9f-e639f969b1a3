use crate::engine::{
    vietnamese_text_validator::{VietnameseValidator, ValidationResult},
    word_boundary_detector::WordBoundaryDetector,
    context_aware_w_processor::ContextAwareWProcessor,
    vietnamese_state_machine::{VietnameseInputStateMachine, StateMachineConfig},
    traits::{ProcessResult, KeyEvent, InputContext, InputMethodProcessor, ProcessorConfig, ConfigError, ProcessorSettings},
    InputMethod, InputState,
};
use std::sync::Arc;
use log::{debug, info, warn};

/// Integrated Vietnamese input processor that coordinates all advanced features
pub struct IntegratedVietnameseProcessor {
    /// Core state machine for Vietnamese input processing
    state_machine: VietnameseInputStateMachine,
    /// Vietnamese text validator
    validator: VietnameseValidator,
    /// Word boundary detector
    boundary_detector: WordBoundaryDetector,
    /// Context-aware W key processor
    w_processor: ContextAwareWProcessor,
    /// Input method configuration
    input_method: InputMethod,
    /// Processor configuration
    config: IntegratedProcessorConfig,
    /// Processing statistics
    stats: ProcessingStatistics,
    /// Performance monitoring
    #[allow(dead_code)]
    performance_monitor: Option<Arc<crate::engine::PerformanceMonitor>>,
}

/// Configuration for the integrated Vietnamese processor
#[derive(Debug, Clone)]
pub struct IntegratedProcessorConfig {
    /// Enable enhanced validation
    pub enhanced_validation: bool,
    /// Enable automatic word boundary detection
    pub auto_word_boundaries: bool,
    /// Enable context-aware W processing
    pub context_aware_w: bool,
    /// Enable spell checking during input
    pub spell_check_enabled: bool,
    /// Enable performance monitoring
    pub performance_monitoring: bool,
    /// Maximum word length before forcing boundary
    pub max_word_length: usize,
    /// Enable debug logging
    pub debug_logging: bool,
    /// Enable correction suggestions
    pub correction_suggestions: bool,
    /// Enable input prediction
    pub input_prediction: bool,
}

impl Default for IntegratedProcessorConfig {
    fn default() -> Self {
        Self {
            enhanced_validation: true,
            auto_word_boundaries: true,
            context_aware_w: true,
            spell_check_enabled: true,
            performance_monitoring: false,
            max_word_length: 20,
            debug_logging: false,
            correction_suggestions: true,
            input_prediction: false,
        }
    }
}

/// Processing statistics for the integrated processor
#[derive(Debug, Clone, Default)]
pub struct ProcessingStatistics {
    /// Total keys processed
    pub total_keys: usize,
    /// Total characters generated
    pub total_characters: usize,
    /// Number of corrections made
    pub corrections_made: usize,
    /// Number of word boundaries detected
    pub word_boundaries: usize,
    /// Number of W key transformations
    pub w_transformations: usize,
    /// Number of validation errors
    pub validation_errors: usize,
    /// Average processing time per key (microseconds)
    pub avg_processing_time_us: f64,
}

impl Default for IntegratedVietnameseProcessor {
    fn default() -> Self {
        Self::new()
    }
}

impl IntegratedVietnameseProcessor {
    /// Create a new integrated Vietnamese processor
    pub fn new() -> Self {
        let config = IntegratedProcessorConfig::default();
        let state_machine_config = StateMachineConfig {
            max_word_length: config.max_word_length,
            auto_boundary_detection: config.auto_word_boundaries,
            context_aware_w: config.context_aware_w,
            spell_check_enabled: config.spell_check_enabled,
            max_history_size: 50,
        };

        Self {
            state_machine: VietnameseInputStateMachine::with_config(state_machine_config),
            validator: VietnameseValidator::new(),
            boundary_detector: WordBoundaryDetector::new(),
            w_processor: ContextAwareWProcessor::new(),
            input_method: InputMethod::Telex,
            config,
            stats: ProcessingStatistics::default(),
            performance_monitor: None,
        }
    }

    /// Create processor with custom configuration
    pub fn with_config(config: IntegratedProcessorConfig) -> Self {
        let mut processor = Self::new();
        processor.config = config;
        processor
    }

    /// Process a key event with integrated Vietnamese processing
    pub fn process_key_integrated(&mut self, key_event: KeyEvent) -> ProcessResult {
        let start_time = std::time::Instant::now();
        
        if self.config.debug_logging {
            debug!("🔤 [INTEGRATED] Processing key: {:?}", key_event);
        }

        // Update statistics
        self.stats.total_keys += 1;

        // Convert key event to character if possible
        let result = if let Some(ch) = key_event.to_char() {
            self.process_character_integrated(ch, key_event.key_code)
        } else {
            self.process_special_key_integrated(key_event)
        };

        // Update performance statistics
        let processing_time = start_time.elapsed();
        self.update_performance_stats(processing_time);

        if self.config.debug_logging {
            debug!("✅ [INTEGRATED] Processing completed in {:?}: {:?}", processing_time, result);
        }

        result
    }

    /// Process a character with integrated features
    fn process_character_integrated(&mut self, ch: char, key_code: u16) -> ProcessResult {
        // Use the state machine for core processing
        let state_result = self.state_machine.process_key(key_code, false);

        // Apply integrated processing based on the state machine result
        match state_result.process_result {
            ProcessResult::Append { text } => {
                self.stats.total_characters += text.len();
                
                // Apply additional validation if enabled
                if self.config.enhanced_validation {
                    if let Some(validation_result) = &state_result.validation_result {
                        if !validation_result.is_valid {
                            self.stats.validation_errors += 1;
                            return self.handle_validation_error(validation_result, &text);
                        }
                    }
                }

                ProcessResult::Append { text }
            }
            ProcessResult::Replace { backspace_count, new_chars } => {
                self.stats.total_characters += new_chars.len();
                
                // Check if this was a W transformation
                if ch == 'w' || ch == 'W' {
                    self.stats.w_transformations += 1;
                    
                    if self.config.debug_logging {
                        info!("🔄 [W_TRANSFORM] Applied W transformation: {} backspaces, {} new chars", 
                             backspace_count, new_chars.len());
                    }
                }

                ProcessResult::Replace { backspace_count, new_chars }
            }
            ProcessResult::ReplaceText { text, backspace_count } => {
                self.stats.total_characters += text.len();
                
                // Check if this was a correction
                if state_result.validation_result.is_some() {
                    self.stats.corrections_made += 1;
                    
                    if self.config.debug_logging {
                        info!("🔧 [CORRECTION] Applied correction: {}", text);
                    }
                }

                ProcessResult::ReplaceText { text, backspace_count }
            }
            other => other,
        }
    }

    /// Process special keys (non-character keys)
    fn process_special_key_integrated(&mut self, key_event: KeyEvent) -> ProcessResult {
        if key_event.is_backspace() {
            self.handle_integrated_backspace()
        } else if key_event.is_delete() {
            self.handle_integrated_delete()
        } else {
            ProcessResult::PassThrough
        }
    }

    /// Handle backspace with integrated features
    fn handle_integrated_backspace(&mut self) -> ProcessResult {
        if self.config.debug_logging {
            debug!("⌫ [INTEGRATED_BACKSPACE] Processing integrated backspace");
        }

        // Use state machine's backspace handling
        let result = self.state_machine.process_key(8, false); // ASCII backspace
        
        match result.process_result {
            ProcessResult::Delete { count } => {
                if self.config.debug_logging {
                    debug!("⌫ [INTEGRATED_BACKSPACE] Deleting {} characters", count);
                }
                ProcessResult::Delete { count }
            }
            ProcessResult::Replace { backspace_count, new_chars } => {
                if self.config.debug_logging {
                    debug!("⌫ [INTEGRATED_BACKSPACE] Restoring previous state: {} backspaces, {} chars", 
                           backspace_count, new_chars.len());
                }
                ProcessResult::Replace { backspace_count, new_chars }
            }
            other => other,
        }
    }

    /// Handle delete with integrated features
    fn handle_integrated_delete(&mut self) -> ProcessResult {
        if self.config.debug_logging {
            debug!("⌦ [INTEGRATED_DELETE] Processing integrated delete");
        }

        // For delete key, typically pass through as it affects characters after cursor
        ProcessResult::PassThrough
    }

    /// Handle validation errors with integrated features
    fn handle_validation_error(&mut self, validation_result: &ValidationResult, original_text: &str) -> ProcessResult {
        if self.config.debug_logging {
            warn!("⚠️ [VALIDATION_ERROR] Validation failed for '{}': {:?}", 
                  original_text, validation_result.error_type);
        }

        if self.config.correction_suggestions {
            if let Some(suggestion) = &validation_result.suggestion {
                if self.config.debug_logging {
                    info!("💡 [SUGGESTION] Applying suggestion: '{}'", suggestion);
                }
                
                self.stats.corrections_made += 1;
                return ProcessResult::ReplaceText {
                    text: suggestion.clone(),
                    backspace_count: original_text.len(),
                };
            }
        }

        // If no suggestion available, just append the original text
        ProcessResult::Append { text: original_text.to_string() }
    }

    /// Update performance statistics
    fn update_performance_stats(&mut self, processing_time: std::time::Duration) {
        let time_us = processing_time.as_micros() as f64;
        
        // Update running average
        let total_samples = self.stats.total_keys as f64;
        self.stats.avg_processing_time_us = 
            (self.stats.avg_processing_time_us * (total_samples - 1.0) + time_us) / total_samples;

        // Log performance warnings if enabled
        if self.config.performance_monitoring && time_us > 1000.0 { // > 1ms
            warn!("⚡ [PERFORMANCE] Slow processing detected: {:.2}μs", time_us);
        }
    }

    /// Get current processing statistics
    pub fn get_statistics(&self) -> &ProcessingStatistics {
        &self.stats
    }

    /// Reset processing statistics
    pub fn reset_statistics(&mut self) {
        self.stats = ProcessingStatistics::default();
    }

    /// Get current word being processed
    pub fn get_current_word(&self) -> String {
        self.state_machine.get_current_word()
    }

    /// Get current input state
    pub fn get_current_state(&self) -> String {
        format!("{:?}", self.state_machine.get_current_state())
    }

    /// Clear all caches for memory management
    pub fn clear_caches(&mut self) {
        self.validator.clear_cache();
        self.boundary_detector.clear_cache();
        self.w_processor.clear_caches();
        self.state_machine.clear_caches();

        if self.config.debug_logging {
            debug!("🧹 [CACHE] Cleared all caches");
        }
    }

    /// Update processor configuration
    pub fn update_config(&mut self, config: IntegratedProcessorConfig) {
        if self.config.debug_logging {
            debug!("⚙️ [CONFIG] Updating processor configuration");
        }

        self.config = config;

        // Update state machine configuration
        let state_machine_config = StateMachineConfig {
            max_word_length: self.config.max_word_length,
            auto_boundary_detection: self.config.auto_word_boundaries,
            context_aware_w: self.config.context_aware_w,
            spell_check_enabled: self.config.spell_check_enabled,
            max_history_size: 50,
        };

        // Note: In a full implementation, we'd need a way to update the state machine config
        // For now, we'll create a new state machine with the new config
        self.state_machine = VietnameseInputStateMachine::with_config(state_machine_config);
    }

    /// Enable or disable debug logging
    pub fn set_debug_logging(&mut self, enabled: bool) {
        self.config.debug_logging = enabled;
    }

    /// Check if the processor is in Vietnamese mode
    pub fn is_vietnamese_mode(&self) -> bool {
        matches!(self.input_method, InputMethod::Telex | InputMethod::VNI | InputMethod::SimpleTelex1 | InputMethod::SimpleTelex2)
    }

    /// Set the input method
    pub fn set_input_method(&mut self, method: InputMethod) {
        if self.config.debug_logging {
            debug!("🔧 [INPUT_METHOD] Changing input method to {:?}", method);
        }

        self.input_method = method;

        // Reset state when changing input methods
        self.state_machine.reset();
    }

    /// Get the current input method
    pub fn get_input_method(&self) -> InputMethod {
        self.input_method
    }

    /// Reset the processor to initial state
    pub fn reset(&mut self) {
        if self.config.debug_logging {
            debug!("🔄 [RESET] Resetting integrated Vietnamese processor");
        }

        self.state_machine.reset();
        self.clear_caches();
        self.reset_statistics();
    }

    /// Process a complete text string (for batch processing)
    pub fn process_text(&mut self, text: &str) -> Vec<ProcessResult> {
        let mut results = Vec::new();

        for ch in text.chars() {
            let key_event = KeyEvent::new(ch as u16, 0);
            let result = self.process_key_integrated(key_event);
            results.push(result);
        }

        results
    }

    /// Get performance metrics summary
    pub fn get_performance_summary(&self) -> String {
        format!(
            "Integrated Vietnamese Processor Performance:\n\
             - Total keys processed: {}\n\
             - Total characters generated: {}\n\
             - Corrections made: {}\n\
             - Word boundaries detected: {}\n\
             - W transformations: {}\n\
             - Validation errors: {}\n\
             - Average processing time: {:.2}μs\n\
             - Correction rate: {:.2}%",
            self.stats.total_keys,
            self.stats.total_characters,
            self.stats.corrections_made,
            self.stats.word_boundaries,
            self.stats.w_transformations,
            self.stats.validation_errors,
            self.stats.avg_processing_time_us,
            if self.stats.total_keys > 0 {
                (self.stats.corrections_made as f64 / self.stats.total_keys as f64) * 100.0
            } else {
                0.0
            }
        )
    }
}

impl InputMethodProcessor for IntegratedVietnameseProcessor {
    fn name(&self) -> &'static str {
        "Integrated Vietnamese Processor"
    }

    fn process_key(&mut self, key: KeyEvent, _context: &mut InputContext) -> ProcessResult {
        self.process_key_integrated(key)
    }

    fn can_handle_key(&self, key: &KeyEvent) -> bool {
        // Can handle all printable characters and some special keys
        key.is_printable() || key.is_backspace() || key.is_delete()
    }

    fn reset(&mut self) {
        self.reset();
    }

    fn reset_after_injection(&mut self) {
        if self.config.debug_logging {
            debug!("🔄 [RESET_AFTER_INJECTION] Resetting integrated Vietnamese processor after injection");
        }
        self.state_machine.reset();
        self.reset_statistics();
    }

    fn get_configuration(&self) -> ProcessorConfig {
        ProcessorConfig {
            enabled: true,
            priority: 100,
            custom_mappings: std::collections::HashMap::new(),
        }
    }

    fn set_configuration(&mut self, _config: ProcessorConfig) -> Result<(), ConfigError> {
        // For now, just return Ok. In a full implementation, we'd map ProcessorConfig
        // to our IntegratedProcessorConfig
        Ok(())
    }

    fn process_char(&mut self, ch: char) -> ProcessResult {
        // Create a dummy KeyEvent and context for compatibility
        let key_event = KeyEvent::new(ch as u16, 0);
        let mut context = InputContext {
            current_state: InputState::default(),
            settings: ProcessorSettings::default(),
            performance_monitor: std::sync::Arc::new(crate::engine::performance::PerformanceMonitor::new()),
        };
        self.process_key(key_event, &mut context)
    }

    fn as_any_mut(&mut self) -> &mut dyn std::any::Any {
        self
    }
}

// Legacy aliases for backward compatibility
pub type EnhancedVietnameseProcessor = IntegratedVietnameseProcessor;
pub type EnhancedProcessorConfig = IntegratedProcessorConfig;
