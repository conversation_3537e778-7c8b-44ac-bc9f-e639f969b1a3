// VNI input method implementation
// Complete implementation for VNI Vietnamese input processing

use crate::engine::{
    traits::{InputMethodProcessor, ProcessResult, KeyEvent, ProcessorConfig, ConfigError, ProcessorSettings},
    state::InputState,
    vietnamese::VietnameseProcessor,
    InputMethod, Encoding, LanguageMode, InputContext,
};
use std::collections::{HashMap, VecDeque};

/// VNI-specific key mappings for Vietnamese characters
#[derive(Debu<PERSON>, Clone)]
pub struct VniKeyMap {
    /// VNI number to Vietnamese character mapping
    vni_map: HashMap<char, HashMap<char, char>>,
    /// Reverse mapping for validation
    reverse_map: HashMap<char, (char, char)>,
}

impl Default for VniKeyMap {
    fn default() -> Self {
        let mut vni_map = HashMap::new();
        let mut reverse_map = HashMap::new();

        // VNI vowel mappings
        let mut a_map = HashMap::new();
        a_map.insert('6', 'â');
        a_map.insert('8', 'ă');
        vni_map.insert('a', a_map);

        let mut e_map = HashMap::new();
        e_map.insert('6', 'ê');
        vni_map.insert('e', e_map);

        let mut o_map = HashMap::new();
        o_map.insert('6', 'ô');
        o_map.insert('7', 'ơ');
        vni_map.insert('o', o_map);

        let mut u_map = HashMap::new();
        u_map.insert('7', 'ư');
        vni_map.insert('u', u_map);

        let mut d_map = HashMap::new();
        d_map.insert('9', 'đ');
        vni_map.insert('d', d_map);

        // Build reverse mapping
        for (base_char, char_map) in &vni_map {
            for (vni_num, result_char) in char_map {
                reverse_map.insert(*result_char, (*base_char, *vni_num));
            }
        }

        Self { vni_map, reverse_map }
    }
}

impl VniKeyMap {
    /// Get Vietnamese character for base character + VNI number
    pub fn get_vni_char(&self, base: char, vni_num: char) -> Option<char> {
        self.vni_map.get(&base)?.get(&vni_num).copied()
    }

    /// Check if a character is a VNI result
    pub fn is_vni_result(&self, ch: char) -> bool {
        self.reverse_map.contains_key(&ch)
    }

    /// Get base character and VNI number for a Vietnamese character
    pub fn get_base_and_vni(&self, ch: char) -> Option<(char, char)> {
        self.reverse_map.get(&ch).copied()
    }
}

/// VNI input method processor with enhanced Vietnamese support
pub struct VniProcessor {
    /// VNI key mapping system
    key_map: VniKeyMap,
    /// Character buffer for processing
    char_buffer: VecDeque<char>,
    /// Current input state
    input_state: InputState,
    /// Vietnamese processor for advanced features
    vietnamese_processor: VietnameseProcessor,
}

impl Default for VniProcessor {
    fn default() -> Self {
        Self::new()
    }
}

impl VniProcessor {
    pub fn new() -> Self {
        let mut vietnamese_processor = VietnameseProcessor::new();
        vietnamese_processor.input_method = InputMethod::VNI;
        vietnamese_processor.encoding = Encoding::Unicode;
        vietnamese_processor.language_mode = LanguageMode::Vietnamese;

        Self {
            key_map: VniKeyMap::default(),
            char_buffer: VecDeque::with_capacity(64),
            input_state: InputState::new(),
            vietnamese_processor,
        }
    }

    /// Process a key with VNI-specific logic
    pub fn process_key_internal(&mut self, key_code: u16, _modifiers: u8) -> Option<String> {
        // Convert key code to character
        if let Some(ch) = self.key_code_to_char(key_code) {
            // Check if this is a VNI number (6, 7, 8, 9)
            if matches!(ch, '6' | '7' | '8' | '9') {
                return self.process_vni_number(ch);
            }

            // Check if this is a tone marker (1-5)
            if matches!(ch, '1' | '2' | '3' | '4' | '5') {
                return self.process_tone_marker(ch);
            }

            // Regular character processing
            self.process_regular_character(ch)
        } else {
            None
        }
    }

    /// Process VNI numbers (6, 7, 8, 9) for vowel modification
    fn process_vni_number(&mut self, vni_num: char) -> Option<String> {
        // Look for the last vowel in the buffer
        if let Some(last_char) = self.char_buffer.back().copied() {
            if let Some(vietnamese_char) = self.key_map.get_vni_char(last_char, vni_num) {
                // Replace the last character with the Vietnamese equivalent
                self.char_buffer.pop_back();
                self.char_buffer.push_back(vietnamese_char);
                return Some(vietnamese_char.to_string());
            }
        }

        // If no valid combination, just add the number
        self.char_buffer.push_back(vni_num);
        Some(vni_num.to_string())
    }

    /// Process tone markers (1-5)
    fn process_tone_marker(&mut self, _tone: char) -> Option<String> {
        // Use the Vietnamese processor for tone application
        let chars: Vec<char> = self.char_buffer.iter().copied().collect();
        let result = self.vietnamese_processor.process_vni(&chars);

        // Update buffer with result
        self.char_buffer.clear();
        self.char_buffer.extend(result.chars());

        Some(result)
    }

    /// Process regular characters
    fn process_regular_character(&mut self, ch: char) -> Option<String> {
        // Add to buffer
        self.char_buffer.push_back(ch);

        // Limit buffer size
        if self.char_buffer.len() > 64 {
            self.char_buffer.pop_front();
        }

        Some(ch.to_string())
    }

    /// Convert key code to character
    fn key_code_to_char(&self, key_code: u16) -> Option<char> {
        if key_code <= 127 {
            Some(key_code as u8 as char)
        } else {
            char::from_u32(key_code as u32)
        }
    }

    /// Reset the processor state
    pub fn reset(&mut self) {
        self.char_buffer.clear();
        self.input_state.reset();
        self.vietnamese_processor.reset();
    }

    /// Get current buffer content
    pub fn get_buffer_content(&self) -> String {
        self.char_buffer.iter().collect()
    }
}

impl InputMethodProcessor for VniProcessor {
    fn name(&self) -> &'static str {
        "VNI"
    }

    fn process_key(&mut self, key: KeyEvent, context: &mut InputContext) -> ProcessResult {
        // Check if Vietnamese mode is active
        if context.current_state.temp_disable {
            return ProcessResult::NoChange;
        }

        // Process the key using VNI logic
        if let Some(result) = self.process_key_internal(key.key_code, key.modifiers) {
            if !result.is_empty() {
                ProcessResult::Replace {
                    backspace_count: 0,
                    new_chars: result.chars().collect(),
                }
            } else {
                ProcessResult::NoChange
            }
        } else {
            ProcessResult::NoChange
        }
    }

    fn can_handle_key(&self, key: &KeyEvent) -> bool {
        // Can handle printable ASCII characters and VNI numbers
        (key.key_code >= 32 && key.key_code <= 126) ||
        matches!(key.key_code as u8 as char, '1'..='9')
    }

    fn reset(&mut self) {
        self.reset();
    }

    fn reset_after_injection(&mut self) {
        self.reset();
    }

    fn get_configuration(&self) -> ProcessorConfig {
        ProcessorConfig::default()
    }

    fn set_configuration(&mut self, _config: ProcessorConfig) -> Result<(), ConfigError> {
        // VNI processor doesn't need complex configuration for now
        Ok(())
    }

    fn process_char(&mut self, ch: char) -> ProcessResult {
        // Create a dummy KeyEvent and context for compatibility
        let key_event = KeyEvent::new(ch as u16, 0);
        let mut context = InputContext {
            current_state: crate::engine::InputState::default(),
            settings: ProcessorSettings::default(),
            performance_monitor: std::sync::Arc::new(crate::engine::performance::PerformanceMonitor::new()),
        };
        self.process_key(key_event, &mut context)
    }

    fn as_any_mut(&mut self) -> &mut dyn std::any::Any {
        self
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_vni_processor_creation() {
        let processor = VniProcessor::new();
        assert_eq!(processor.name(), "VNI");
        assert!(processor.char_buffer.is_empty());
    }

    #[test]
    fn test_vni_key_map_creation() {
        let key_map = VniKeyMap::default();

        // Test basic VNI mappings
        assert_eq!(key_map.get_vni_char('a', '6'), Some('â'));
        assert_eq!(key_map.get_vni_char('a', '8'), Some('ă'));
        assert_eq!(key_map.get_vni_char('e', '6'), Some('ê'));
        assert_eq!(key_map.get_vni_char('o', '6'), Some('ô'));
        assert_eq!(key_map.get_vni_char('o', '7'), Some('ơ'));
        assert_eq!(key_map.get_vni_char('u', '7'), Some('ư'));
        assert_eq!(key_map.get_vni_char('d', '9'), Some('đ'));
    }

    #[test]
    fn test_vni_reverse_mapping() {
        let key_map = VniKeyMap::default();

        // Test reverse mappings
        assert_eq!(key_map.get_base_and_vni('â'), Some(('a', '6')));
        assert_eq!(key_map.get_base_and_vni('ă'), Some(('a', '8')));
        assert_eq!(key_map.get_base_and_vni('ê'), Some(('e', '6')));
        assert_eq!(key_map.get_base_and_vni('ô'), Some(('o', '6')));
        assert_eq!(key_map.get_base_and_vni('ơ'), Some(('o', '7')));
        assert_eq!(key_map.get_base_and_vni('ư'), Some(('u', '7')));
        assert_eq!(key_map.get_base_and_vni('đ'), Some(('d', '9')));
    }

    #[test]
    fn test_vni_character_detection() {
        let key_map = VniKeyMap::default();

        // Test VNI result detection
        assert!(key_map.is_vni_result('â'));
        assert!(key_map.is_vni_result('ă'));
        assert!(key_map.is_vni_result('ê'));
        assert!(key_map.is_vni_result('đ'));
        assert!(!key_map.is_vni_result('a'));
        assert!(!key_map.is_vni_result('e'));
        assert!(!key_map.is_vni_result('x'));
    }

    #[test]
    fn test_key_code_conversion() {
        let processor = VniProcessor::new();

        // Test ASCII character conversion
        assert_eq!(processor.key_code_to_char(97), Some('a'));
        assert_eq!(processor.key_code_to_char(54), Some('6'));
        assert_eq!(processor.key_code_to_char(49), Some('1'));
    }

    #[test]
    fn test_can_handle_key() {
        let processor = VniProcessor::new();

        // Test key handling capability
        let key_a = KeyEvent {
            key_code: 97,
            modifiers: 0,
            event_type: crate::engine::traits::KeyEventType::KeyDown,
            timestamp: std::time::Instant::now(),
        }; // 'a'
        let key_6 = KeyEvent {
            key_code: 54,
            modifiers: 0,
            event_type: crate::engine::traits::KeyEventType::KeyDown,
            timestamp: std::time::Instant::now(),
        }; // '6'
        let key_1 = KeyEvent {
            key_code: 49,
            modifiers: 0,
            event_type: crate::engine::traits::KeyEventType::KeyDown,
            timestamp: std::time::Instant::now(),
        }; // '1'
        let key_ctrl = KeyEvent {
            key_code: 17,
            modifiers: 1,
            event_type: crate::engine::traits::KeyEventType::KeyDown,
            timestamp: std::time::Instant::now(),
        }; // Ctrl

        assert!(processor.can_handle_key(&key_a));
        assert!(processor.can_handle_key(&key_6));
        assert!(processor.can_handle_key(&key_1));
        assert!(!processor.can_handle_key(&key_ctrl));
    }

    #[test]
    fn test_buffer_management() {
        let mut processor = VniProcessor::new();

        // Test buffer operations
        assert_eq!(processor.get_buffer_content(), "");

        // Add some characters
        processor.process_regular_character('a');
        processor.process_regular_character('b');
        assert_eq!(processor.get_buffer_content(), "ab");

        // Reset buffer
        processor.reset();
        assert_eq!(processor.get_buffer_content(), "");
    }

    #[test]
    fn test_vni_number_processing() {
        let mut processor = VniProcessor::new();

        // Add base character
        processor.process_regular_character('a');
        assert_eq!(processor.get_buffer_content(), "a");

        // Add VNI number to modify it
        let result = processor.process_vni_number('6');
        assert_eq!(result, Some("â".to_string()));
        assert_eq!(processor.get_buffer_content(), "â");
    }

    #[test]
    fn test_invalid_vni_combination() {
        let mut processor = VniProcessor::new();

        // Add character that doesn't have VNI mapping
        processor.process_regular_character('x');

        // Try to apply VNI number
        let result = processor.process_vni_number('6');
        assert_eq!(result, Some("6".to_string()));
        assert_eq!(processor.get_buffer_content(), "x6");
    }

    #[test]
    fn test_processor_reset() {
        let mut processor = VniProcessor::new();

        // Add some content
        processor.process_regular_character('a');
        processor.process_vni_number('6');
        assert!(!processor.get_buffer_content().is_empty());

        // Reset and verify
        processor.reset();
        assert_eq!(processor.get_buffer_content(), "");
    }
}
