use openkey_rust_lib::engine::{VietnameseEngine, InputMethod};
use openkey_rust_lib::engine::traits::ProcessResult;

fn main() {
    println!("🔍 Debug Tone Removal Bug");
    println!("========================");
    
    // Create a fresh engine
    let mut engine = VietnameseEngine::new();
    engine.set_input_method(InputMethod::Telex);
    
    println!("\n📊 Testing tone removal sequence:");
    println!("Expected: 'a' -> 'a' -> 'â' -> 'a' -> 'a' -> 'â' (tone removal cycle)");
    println!("Bug: Getting 'âaâââââââââââ' instead");
    
    // Reset engine
    engine.reset();
    
    println!("\n🧪 Step 1: Type 'a'");
    let result1 = engine.process_character('a');
    println!("  'a': {:?}", result1);
    print_engine_state(&engine);
    
    println!("\n🧪 Step 2: Type 'a' (should create 'â')");
    let result2 = engine.process_character('a');
    println!("  'a': {:?}", result2);
    print_engine_state(&engine);
    
    println!("\n🧪 Step 3: Type 'a' (should remove tone, back to 'aa')");
    let result3 = engine.process_character('a');
    println!("  'a': {:?}", result3);
    print_engine_state(&engine);
    
    match result3 {
        ProcessResult::Replace { backspace_count, new_chars } => {
            let result_text: String = new_chars.iter().collect();
            println!("  📊 Analysis:");
            println!("    - Result text: '{}'", result_text);
            println!("    - Backspace count: {}", backspace_count);
            println!("    - Expected: 'aa' (tone removal)");
            
            if result_text.contains("â") && result_text.len() > 2 {
                println!("    ❌ BUG DETECTED: Multiple 'â' characters found!");
                println!("    ❌ This is the 'âaâââââââââââ' bug");
            } else if result_text == "aa" {
                println!("    ✅ Correct: Tone removal working");
            } else {
                println!("    ⚠️  Unexpected result");
            }
        }
        ProcessResult::NoChange => {
            println!("  ⚠️  NoChange - might be correct if buffer handles it differently");
        }
        _ => {
            println!("  ❌ Unexpected result type");
        }
    }
    
    println!("\n🧪 Step 4: Type 'a' again (should create 'â' again)");
    let result4 = engine.process_character('a');
    println!("  'a': {:?}", result4);
    print_engine_state(&engine);
    
    println!("\n🧪 Step 5: Type 'a' again (should remove tone again)");
    let result5 = engine.process_character('a');
    println!("  'a': {:?}", result5);
    print_engine_state(&engine);
    
    // Test with different scenarios
    println!("\n📊 Testing other tone removal scenarios:");
    
    // Test oo -> ô -> ooo
    engine.reset();
    println!("\n🧪 Test: 'o' -> 'o' -> 'ô' -> 'o' (tone removal)");
    let _r1 = engine.process_character('o');
    let _r2 = engine.process_character('o');
    let result_o = engine.process_character('o');
    println!("  Third 'o': {:?}", result_o);
    
    // Test aw -> ă -> aw
    engine.reset();
    println!("\n🧪 Test: 'a' -> 'w' -> 'ă' -> 'w' (tone removal)");
    let _r3 = engine.process_character('a');
    let _r4 = engine.process_character('w');
    let result_w = engine.process_character('w');
    println!("  Second 'w': {:?}", result_w);
}

fn print_engine_state(engine: &VietnameseEngine) {
    println!("  🔍 Engine state:");
    println!("    - Language mode: {:?}", engine.language_mode);
    println!("    - Input method: {:?}", engine.input_method);
    // Note: We can't access internal buffer state directly from VietnameseEngine
    // The actual buffer is in the VietnameseProcessor inside the engine
}
