use openkey_rust_lib::engine::{VietnameseEngine, InputMethod};
use openkey_rust_lib::engine::traits::ProcessResult;

fn main() {
    println!("🔍 Debug Double Character Issue");
    println!("===============================");
    
    // Create a fresh engine
    let mut engine = VietnameseEngine::new();
    engine.set_input_method(InputMethod::Telex);
    
    println!("\n📊 Testing single character processing:");
    
    // Test single characters that should NOT be transformed
    let test_chars = ['a', 'b', 'c', 'x', 'y', 'z', '1', '2', '3'];
    
    for ch in test_chars {
        // Reset engine completely for each test
        engine.reset();

        let result = engine.process_character(ch);
        println!("  '{}': {:?}", ch, result);
        
        match result {
            ProcessResult::NoChange => {
                println!("    ✅ Correct: NoChange (character passes through normally)");
            }
            ProcessResult::PassThrough => {
                println!("    ✅ Correct: PassThrough (character passes through normally)");
            }
            ProcessResult::Replace { backspace_count, new_chars } => {
                if backspace_count == 0 && new_chars.len() == 1 && new_chars[0] == ch {
                    println!("    ⚠️  ISSUE: Replace with same character (causes double typing!)");
                    println!("        Should return NoChange instead");
                } else {
                    println!("    🤔 Replace with different content: bs={}, chars={:?}", backspace_count, new_chars);
                }
            }
            _ => {
                println!("    🤔 Unexpected result type");
            }
        }
    }
    
    println!("\n📊 Testing Vietnamese transformations (should use Replace):");
    
    // Test Vietnamese transformations that SHOULD use Replace
    let vietnamese_tests = [
        ("aa", "â"),
        ("aw", "ă"), 
        ("oo", "ô"),
        ("ow", "ơ"),
        ("uw", "ư"),
    ];
    
    for (input, expected) in vietnamese_tests {
        // Reset engine completely for each test
        engine.reset();
        
        println!("\n  Testing '{}' -> '{}':", input, expected);
        
        let mut last_result = ProcessResult::NoChange;
        for ch in input.chars() {
            last_result = engine.process_character(ch);
            println!("    '{}': {:?}", ch, last_result);
        }
        
        match last_result {
            ProcessResult::Replace { new_chars, .. } => {
                let result_text: String = new_chars.iter().collect();
                if result_text.contains(expected) {
                    println!("    ✅ Correct: Vietnamese transformation working");
                } else {
                    println!("    ❌ Wrong: Expected '{}', got '{}'", expected, result_text);
                }
            }
            _ => {
                println!("    ❌ Wrong: Expected Replace result for Vietnamese transformation");
            }
        }
    }
    
    println!("\n📊 Summary:");
    println!("- Single characters should return NoChange or PassThrough");
    println!("- Vietnamese transformations should return Replace");
    println!("- Replace with same character causes double typing!");
}
