use openkey_rust_lib::engine::{VietnameseEngine, InputMethod};
use openkey_rust_lib::engine::traits::ProcessResult;

fn main() {
    println!("🔍 Comprehensive Tone Removal Test");
    println!("==================================");
    
    // Test the complete cycle multiple times
    test_aa_cycle();
    test_oo_cycle();
    test_aw_cycle();
}

fn test_aa_cycle() {
    println!("\n📊 Testing 'aa' ↔ 'â' cycle:");
    
    let mut engine = VietnameseEngine::new();
    engine.set_input_method(InputMethod::Telex);
    engine.reset();
    
    // Cycle 1: a -> aa -> â -> aa -> aâ -> âa -> aaa -> aaâ -> âaa -> âaâ
    println!("\n🔄 Cycle 1:");
    
    println!("Step 1: 'a'");
    let r1 = engine.process_character('a');
    println!("  Result: {:?}", r1);
    
    println!("Step 2: 'a' (should make 'â')");
    let r2 = engine.process_character('a');
    println!("  Result: {:?}", r2);
    
    println!("Step 3: 'a' (should remove tone -> 'aa')");
    let r3 = engine.process_character('a');
    println!("  Result: {:?}", r3);
    
    println!("Step 4: 'a' (should make 'aâ' or 'âa')");
    let r4 = engine.process_character('a');
    println!("  Result: {:?}", r4);
    
    println!("Step 5: 'a' (tone removal again)");
    let r5 = engine.process_character('a');
    println!("  Result: {:?}", r5);
    
    println!("Step 6: 'a' (should make some transformation)");
    let r6 = engine.process_character('a');
    println!("  Result: {:?}", r6);
    
    // Test what happens with a space (word boundary)
    println!("\nStep 7: ' ' (space - word boundary)");
    let r7 = engine.process_character(' ');
    println!("  Result: {:?}", r7);
    
    // Start fresh after space
    println!("Step 8: 'a' (fresh start)");
    let r8 = engine.process_character('a');
    println!("  Result: {:?}", r8);
    
    println!("Step 9: 'a' (should make 'â')");
    let r9 = engine.process_character('a');
    println!("  Result: {:?}", r9);
    
    println!("Step 10: 'a' (should remove tone -> 'aa')");
    let r10 = engine.process_character('a');
    println!("  Result: {:?}", r10);
}

fn test_oo_cycle() {
    println!("\n📊 Testing 'oo' ↔ 'ô' cycle:");
    
    let mut engine = VietnameseEngine::new();
    engine.set_input_method(InputMethod::Telex);
    engine.reset();
    
    println!("Step 1: 'o'");
    let r1 = engine.process_character('o');
    println!("  Result: {:?}", r1);
    
    println!("Step 2: 'o' (should make 'ô')");
    let r2 = engine.process_character('o');
    println!("  Result: {:?}", r2);
    
    println!("Step 3: 'o' (should remove tone -> 'oo')");
    let r3 = engine.process_character('o');
    println!("  Result: {:?}", r3);
    
    println!("Step 4: 'o' (should make 'oô' or 'ôo')");
    let r4 = engine.process_character('o');
    println!("  Result: {:?}", r4);
}

fn test_aw_cycle() {
    println!("\n📊 Testing 'aw' ↔ 'ă' cycle:");
    
    let mut engine = VietnameseEngine::new();
    engine.set_input_method(InputMethod::Telex);
    engine.reset();
    
    println!("Step 1: 'a'");
    let r1 = engine.process_character('a');
    println!("  Result: {:?}", r1);
    
    println!("Step 2: 'w' (should make 'ă')");
    let r2 = engine.process_character('w');
    println!("  Result: {:?}", r2);
    
    println!("Step 3: 'w' (should remove tone -> 'aw')");
    let r3 = engine.process_character('w');
    println!("  Result: {:?}", r3);
    
    println!("Step 4: 'w' (should make 'aw' + 'w' = 'ăw')");
    let r4 = engine.process_character('w');
    println!("  Result: {:?}", r4);
}
