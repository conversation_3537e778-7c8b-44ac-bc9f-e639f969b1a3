use std::sync::{Arc, Mutex};
use std::collections::HashMap;
use tauri::{Manager, State};
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};
use crate::debug::{DebugEventType, DebugLevel, get_global_debugger};
use crate::debug::tests::{VietnameseEngineTestSuite, TestResult};
use crate::debug::diagnostics::{VietnameseInputDiagnostics, DiagnosticReport};

pub mod config;
pub mod debug;
pub mod engine;
mod features;
pub mod platform;
mod settings;
pub mod quick_telex_tests;
// pub mod smart_features_tests; // Removed due to API mismatches - needs reimplementation
mod tray;

// Tests are now in src/tests/ directory

use engine::VietnameseEngine;
use engine::performance::ProductionPerformanceSummary;
use features::macro_system::MacroData;
use features::application_profiles::ApplicationProfile;
use features::profile_manager::PersistentProfileManager;
use settings::{OpenKeySettings, SettingsManager};
use config::settings::OpenKeyConfig;

#[cfg(target_os = "macos")]
use platform::macos::MacOSIntegration;

#[cfg(target_os = "macos")]
use macos_permissions::Permission;

// Application state
type AppState = Arc<Mutex<OpenKeySettings>>;
type EngineState = Arc<Mutex<VietnameseEngine>>;
type ConfigState = Arc<Mutex<OpenKeyConfig>>;
type ProfileManagerState = Arc<Mutex<PersistentProfileManager>>;

#[cfg(target_os = "macos")]
type MacOSState = Arc<Mutex<MacOSIntegration>>;

// Tauri commands for frontend communication
#[tauri::command]
fn greet(name: &str) -> String {
    format!("Hello, {}! You've been greeted from Rust!", name)
}

#[tauri::command]
async fn toggle_language_mode(
    state: State<'_, AppState>,
    engine_state: State<'_, EngineState>,
) -> Result<bool, String> {
    log::info!("🔄 [LANGUAGE_MODE] Toggling language mode...");

    let mut settings = state.lock().map_err(|e| {
        log::error!("❌ [LANGUAGE_MODE] Failed to lock app state: {}", e);
        e.to_string()
    })?;

    let mut engine = engine_state.lock().map_err(|e| {
        log::error!("❌ [LANGUAGE_MODE] Failed to lock engine state: {}", e);
        e.to_string()
    })?;

    settings.language_mode = !settings.language_mode;

    // Update engine language mode
    engine.language_mode = if settings.language_mode {
        engine::LanguageMode::Vietnamese
    } else {
        engine::LanguageMode::English
    };

    let mode_str = if settings.language_mode { "Vietnamese" } else { "English" };
    log::info!("✅ [LANGUAGE_MODE] Language mode switched to: {} (engine: {:?})", mode_str, engine.language_mode);

    Ok(settings.language_mode)
}

#[tauri::command]
async fn set_input_method(method: u8, state: State<'_, AppState>) -> Result<(), String> {
    let mut settings = state.lock().map_err(|e| e.to_string())?;
    settings.input_method = method as i32;
    Ok(())
}

#[tauri::command]
async fn set_encoding(encoding: u8, state: State<'_, AppState>) -> Result<(), String> {
    let mut settings = state.lock().map_err(|e| e.to_string())?;
    settings.encoding = encoding as i32;
    Ok(())
}

// Vietnamese input processing commands
#[tauri::command]
async fn process_vietnamese_input(
    input: String,
    method: u8,
    encoding: u8,
    engine_state: State<'_, EngineState>,
) -> Result<String, String> {
    log::info!("🇻🇳 [VIETNAMESE_INPUT] Processing input: '{}' with method: {} encoding: {}", input, method, encoding);

    let mut engine = engine_state.lock().map_err(|e| {
        log::error!("❌ [VIETNAMESE_INPUT] Failed to lock engine state: {}", e);
        e.to_string()
    })?;

    // Convert method and encoding from u8 to enum
    let input_method = match method {
        0 => engine::InputMethod::Telex,
        1 => engine::InputMethod::VNI,
        2 => engine::InputMethod::SimpleTelex1,
        3 => engine::InputMethod::SimpleTelex2,
        _ => {
            log::error!("❌ [VIETNAMESE_INPUT] Invalid input method: {}", method);
            return Err("Invalid input method".to_string());
        }
    };

    let target_encoding = match encoding {
        0 => engine::Encoding::Unicode,
        1 => engine::Encoding::Tcvn3,
        2 => engine::Encoding::VniWindows,
        3 => engine::Encoding::UnicodeCompound,
        4 => engine::Encoding::VietnameseLocale,
        _ => {
            log::error!("❌ [VIETNAMESE_INPUT] Invalid encoding: {}", encoding);
            return Err("Invalid encoding".to_string());
        }
    };

    log::info!("🔧 [VIETNAMESE_INPUT] Setting engine - method: {:?}, encoding: {:?}", input_method, target_encoding);

    // Set engine configuration
    engine.input_method = input_method;
    engine.encoding = target_encoding;

    log::info!("📝 [VIETNAMESE_INPUT] Current engine state - language_mode: {:?}, input_method: {:?}, encoding: {:?}",
               engine.language_mode, engine.input_method, engine.encoding);

    // Process the input string character by character
    let input_chars: Vec<char> = input.chars().collect();
    let mut result = String::new();

    log::info!("🔄 [VIETNAMESE_INPUT] Processing {} characters", input_chars.len());

    for (i, &ch) in input_chars.iter().enumerate() {
        log::debug!("🔤 [VIETNAMESE_INPUT] Processing char {}/{}: '{}' (code: {})", i + 1, input_chars.len(), ch, ch as u16);

        if let Some(processed) = engine.process_key(ch as u16, 0) {
            log::debug!("✅ [VIETNAMESE_INPUT] Char '{}' processed to: '{}'", ch, processed);
            result.push_str(&processed);
        } else {
            log::debug!("⚪ [VIETNAMESE_INPUT] Char '{}' not processed, keeping original", ch);
            result.push(ch);
        }
    }

    log::info!("🎯 [VIETNAMESE_INPUT] Final result: '{}' -> '{}'", input, result);
    Ok(result)
}

#[tauri::command]
async fn process_key_event(
    key_code: u16,
    modifiers: u8,
    engine_state: State<'_, EngineState>,
) -> Result<Option<String>, String> {
    log::info!("⌨️  [KEY_EVENT] Processing key_code: {} modifiers: {}", key_code, modifiers);

    let mut engine = engine_state.lock().map_err(|e| {
        log::error!("❌ [KEY_EVENT] Failed to lock engine state: {}", e);
        e.to_string()
    })?;

    log::debug!("🔧 [KEY_EVENT] Engine state - language_mode: {:?}, input_method: {:?}, encoding: {:?}",
                engine.language_mode, engine.input_method, engine.encoding);

    // Process the key event through the Vietnamese engine
    let result = engine.process_key(key_code, modifiers);

    match &result {
        Some(processed) => log::info!("✅ [KEY_EVENT] Key {} processed to: '{}'", key_code, processed),
        None => log::debug!("⚪ [KEY_EVENT] Key {} not processed", key_code),
    }

    Ok(result)
}

#[tauri::command]
async fn start_vietnamese_input_capture(
    #[cfg(target_os = "macos")] macos_state: State<'_, MacOSState>,
) -> Result<String, String> {
    log::info!("🚀 [CAPTURE] Starting Vietnamese input capture...");

    #[cfg(target_os = "macos")]
    {
        let macos = macos_state.lock().map_err(|e| {
            log::error!("❌ [CAPTURE] Failed to lock macOS state: {}", e);
            e.to_string()
        })?;

        match macos.start_global_event_capture() {
            Ok(_) => {
                log::info!("✅ [CAPTURE] Vietnamese input capture started successfully");
                Ok("Vietnamese input capture started successfully".to_string())
            }
            Err(e) => {
                log::error!("❌ [CAPTURE] Failed to start capture: {}", e);
                Err(e)
            }
        }
    }

    #[cfg(not(target_os = "macos"))]
    {
        log::error!("❌ [CAPTURE] Vietnamese input capture only supported on macOS");
        Err("Vietnamese input capture only supported on macOS".to_string())
    }
}

#[tauri::command]
async fn stop_vietnamese_input_capture(
    #[cfg(target_os = "macos")] macos_state: State<'_, MacOSState>,
) -> Result<String, String> {
    log::info!("🛑 [CAPTURE] Stopping Vietnamese input capture...");

    #[cfg(target_os = "macos")]
    {
        let macos = macos_state.lock().map_err(|e| {
            log::error!("❌ [CAPTURE] Failed to lock macOS state: {}", e);
            e.to_string()
        })?;

        macos.stop_global_event_capture();
        log::info!("✅ [CAPTURE] Vietnamese input capture stopped successfully");
        Ok("Vietnamese input capture stopped successfully".to_string())
    }

    #[cfg(not(target_os = "macos"))]
    {
        log::error!("❌ [CAPTURE] Vietnamese input capture only supported on macOS");
        Err("Vietnamese input capture only supported on macOS".to_string())
    }
}

#[tauri::command]
async fn get_engine_status(engine_state: State<'_, EngineState>) -> Result<String, String> {
    log::info!("📊 [ENGINE_STATUS] Getting engine status...");

    let engine = engine_state.lock().map_err(|e| {
        log::error!("❌ [ENGINE_STATUS] Failed to lock engine state: {}", e);
        e.to_string()
    })?;

    let status = format!(
        "🔧 Vietnamese Engine Status:\n\
         - Language Mode: {:?}\n\
         - Input Method: {:?}\n\
         - Encoding: {:?}\n\
         - Quick Telex: {}\n\
         - Spell Check: {}\n\
         - Modern Orthography: {}\n\
         - Restore if Wrong: {}",
        engine.language_mode,
        engine.input_method,
        engine.encoding,
        engine.quick_telex,
        engine.spell_check_enabled,
        engine.modern_orthography,
        engine.restore_if_wrong
    );

    log::info!("📊 [ENGINE_STATUS] {}", status);
    Ok(status)
}

#[tauri::command]
async fn get_production_performance_summary(engine_state: State<'_, EngineState>) -> Result<ProductionPerformanceSummary, String> {
    log::info!("📊 [PERFORMANCE] Getting production performance summary...");

    let engine = engine_state.lock().map_err(|e| {
        log::error!("❌ [PERFORMANCE] Failed to lock engine state: {}", e);
        e.to_string()
    })?;

    let summary = engine.performance_monitor.get_production_summary();
    log::info!("📊 [PERFORMANCE] Performance summary generated: status={:?}, p95={}μs, throughput={:.2}/s",
               summary.status, summary.latency_p95.as_micros(), summary.throughput);

    Ok(summary)
}

#[tauri::command]
async fn get_settings(state: State<'_, AppState>) -> Result<OpenKeySettings, String> {
    let settings = state.lock().map_err(|e| e.to_string())?;
    Ok(settings.clone())
}

#[tauri::command]
async fn set_quick_telex(
    enabled: bool,
    state: State<'_, AppState>,
    engine_state: State<'_, EngineState>,
) -> Result<(), String> {
    let mut settings = state.lock().map_err(|e| e.to_string())?;
    settings.quick_telex = enabled;

    // Update engine configuration
    let mut engine = engine_state.lock().map_err(|e| e.to_string())?;
    engine.quick_telex = enabled;

    // Save settings to file
    SettingsManager::save_settings(&settings).map_err(|e| e.to_string())?;

    Ok(())
}

#[tauri::command]
async fn update_setting(
    key: String,
    value: bool,
    state: State<'_, AppState>,
    engine_state: State<'_, EngineState>,
) -> Result<(), String> {
    let mut settings = state.lock().map_err(|e| e.to_string())?;
    let mut engine = engine_state.lock().map_err(|e| e.to_string())?;

    match key.as_str() {
        "quick_telex" => {
            settings.quick_telex = value;
            engine.quick_telex = value;
        }
        "check_spelling" => {
            settings.check_spelling = value;
            engine.spell_check_enabled = value;
        }
        "enable_spell_check" => {
            // Frontend alias for check_spelling
            settings.check_spelling = value;
            engine.spell_check_enabled = value;
        }
        "modern_orthography" => {
            settings.modern_orthography = value;
            engine.modern_orthography = value;
        }
        "restore_if_invalid" => {
            settings.restore_if_invalid = value;
            engine.restore_if_wrong = value;
        }
        "auto_restore_on_error" => {
            // Frontend alias for restore_if_invalid
            settings.restore_if_invalid = value;
            engine.restore_if_wrong = value;
        }
        "smart_switch_key" => settings.smart_switch_key = value,
        "quick_start_consonant" => settings.quick_start_consonant = value,
        "quick_end_consonant" => settings.quick_end_consonant = value,
        "free_mark" => settings.free_mark = value,
        "fix_recommend_browser" => {
            settings.fix_recommend_browser = value;
            engine.fix_browser_recommend = value;
        }
        _ => return Err(format!("Unknown setting key: {}", key)),
    }

    // Save settings to file
    SettingsManager::save_settings(&settings).map_err(|e| e.to_string())?;

    Ok(())
}

// Macro management commands
#[tauri::command]
async fn add_macro(
    trigger: String,
    content: String,
    engine_state: State<'_, EngineState>,
) -> Result<(), String> {
    let mut engine = engine_state.lock().map_err(|e| e.to_string())?;
    engine
        .macro_system_mut()
        .add_macro(trigger, content)
        .map_err(|e| e.to_string())?;
    Ok(())
}

#[tauri::command]
async fn remove_macro(
    trigger: String,
    engine_state: State<'_, EngineState>,
) -> Result<bool, String> {
    let mut engine = engine_state.lock().map_err(|e| e.to_string())?;
    let removed = engine.macro_system_mut().remove_macro(&trigger);
    Ok(removed.is_some())
}

#[tauri::command]
async fn get_all_macros(engine_state: State<'_, EngineState>) -> Result<Vec<MacroData>, String> {
    let engine = engine_state.lock().map_err(|e| e.to_string())?;
    let macros = engine
        .macro_system()
        .get_all_macros()
        .into_iter()
        .cloned()
        .collect();
    Ok(macros)
}

#[tauri::command]
async fn clear_all_macros(engine_state: State<'_, EngineState>) -> Result<(), String> {
    let mut engine = engine_state.lock().map_err(|e| e.to_string())?;
    engine.macro_system_mut().clear_all();
    Ok(())
}

#[tauri::command]
async fn set_macro_enabled(
    enabled: bool,
    engine_state: State<'_, EngineState>,
) -> Result<(), String> {
    let mut engine = engine_state.lock().map_err(|e| e.to_string())?;
    engine.macro_system_mut().set_enabled(enabled);
    Ok(())
}

#[tauri::command]
async fn set_macro_auto_caps(
    enabled: bool,
    engine_state: State<'_, EngineState>,
) -> Result<(), String> {
    let mut engine = engine_state.lock().map_err(|e| e.to_string())?;
    engine.macro_system_mut().set_auto_caps_enabled(enabled);
    Ok(())
}

#[tauri::command]
async fn save_macros_to_file(
    file_path: String,
    engine_state: State<'_, EngineState>,
) -> Result<(), String> {
    let engine = engine_state.lock().map_err(|e| e.to_string())?;
    engine
        .macro_system()
        .save_to_file(&file_path)
        .map_err(|e| e.to_string())?;
    Ok(())
}

#[tauri::command]
async fn load_macros_from_file(
    file_path: String,
    append: bool,
    engine_state: State<'_, EngineState>,
) -> Result<(), String> {
    let mut engine = engine_state.lock().map_err(|e| e.to_string())?;
    engine
        .macro_system_mut()
        .load_from_file(&file_path, append)
        .map_err(|e| e.to_string())?;
    Ok(())
}

#[tauri::command]
async fn export_macros_json(
    file_path: String,
    engine_state: State<'_, EngineState>,
) -> Result<(), String> {
    let engine = engine_state.lock().map_err(|e| e.to_string())?;
    engine
        .macro_system()
        .export_to_json(&file_path)
        .map_err(|e| e.to_string())?;
    Ok(())
}

#[tauri::command]
async fn import_macros_json(
    file_path: String,
    append: bool,
    engine_state: State<'_, EngineState>,
) -> Result<(), String> {
    let mut engine = engine_state.lock().map_err(|e| e.to_string())?;
    engine
        .macro_system_mut()
        .import_from_json(&file_path, append)
        .map_err(|e| e.to_string())?;
    Ok(())
}

#[tauri::command]
async fn get_macro_count(engine_state: State<'_, EngineState>) -> Result<usize, String> {
    let engine = engine_state.lock().map_err(|e| e.to_string())?;
    Ok(engine.macro_system().macro_count())
}

#[tauri::command]
async fn validate_macro(
    trigger: String,
    content: String,
    engine_state: State<'_, EngineState>,
) -> Result<bool, String> {
    let engine = engine_state.lock().map_err(|e| e.to_string())?;

    // Check if trigger is empty
    if trigger.is_empty() {
        return Err("Trigger cannot be empty".to_string());
    }

    // Check if content is empty
    if content.is_empty() {
        return Err("Content cannot be empty".to_string());
    }

    // Check trigger length
    if trigger.len() > 50 {
        return Err("Trigger cannot exceed 50 characters".to_string());
    }

    // Check for invalid characters
    if trigger.contains(char::is_whitespace) {
        return Err("Trigger cannot contain spaces".to_string());
    }

    if trigger
        .chars()
        .any(|c| matches!(c, ':' | ';' | ',' | '.' | '!' | '?'))
    {
        return Err("Trigger cannot contain punctuation marks".to_string());
    }

    // Check for conflicts
    if engine.macro_system().has_macro(&trigger) {
        return Err(format!("Macro with trigger '{}' already exists", trigger));
    }

    Ok(true)
}

#[tauri::command]
async fn test_macro_expansion(
    trigger: String,
    content: String,
    auto_caps: bool,
) -> Result<Vec<String>, String> {
    let examples = vec![
        format!("{} → {}", trigger, content),
        if auto_caps {
            format!(
                "{} → {}",
                capitalize_first_letter(&trigger),
                capitalize_first_letter(&content)
            )
        } else {
            format!("{} → {}", trigger, content)
        },
        if auto_caps {
            format!("{} → {}", trigger.to_uppercase(), content.to_uppercase())
        } else {
            format!("{} → {}", trigger, content)
        },
    ];

    Ok(examples)
}

fn capitalize_first_letter(s: &str) -> String {
    let mut chars = s.chars();
    match chars.next() {
        None => String::new(),
        Some(first) => first.to_uppercase().collect::<String>() + chars.as_str(),
    }
}

// Profile management commands
#[tauri::command]
async fn get_all_profiles(
    profile_manager_state: State<'_, ProfileManagerState>,
) -> Result<Vec<ApplicationProfile>, String> {
    let manager = profile_manager_state.lock().map_err(|e| e.to_string())?;
    let profiles = manager.list_profiles()
        .into_iter()
        .cloned()
        .collect();
    Ok(profiles)
}

#[tauri::command]
async fn create_profile(
    profile: ApplicationProfile,
    profile_manager_state: State<'_, ProfileManagerState>,
) -> Result<(), String> {
    let mut manager = profile_manager_state.lock().map_err(|e| e.to_string())?;
    manager.add_profile(profile).map_err(|e| e.to_string())?;
    Ok(())
}

#[tauri::command]
async fn update_profile(
    profile: ApplicationProfile,
    profile_manager_state: State<'_, ProfileManagerState>,
) -> Result<(), String> {
    let mut manager = profile_manager_state.lock().map_err(|e| e.to_string())?;
    manager.update_profile(profile).map_err(|e| e.to_string())?;
    Ok(())
}

#[tauri::command]
async fn delete_profile(
    profile_id: String,
    profile_manager_state: State<'_, ProfileManagerState>,
) -> Result<(), String> {
    let mut manager = profile_manager_state.lock().map_err(|e| e.to_string())?;
    manager.remove_profile(&profile_id).map_err(|e| e.to_string())?;
    Ok(())
}

#[tauri::command]
async fn get_profile_by_id(
    profile_id: String,
    profile_manager_state: State<'_, ProfileManagerState>,
) -> Result<Option<ApplicationProfile>, String> {
    let manager = profile_manager_state.lock().map_err(|e| e.to_string())?;
    Ok(manager.get_profile(&profile_id).cloned())
}

#[tauri::command]
async fn get_running_applications(
    profile_manager_state: State<'_, ProfileManagerState>,
) -> Result<Vec<serde_json::Value>, String> {

    // Get all existing profiles to check assignments
    let app_to_profile = {
        let profile_manager = profile_manager_state.lock().map_err(|e| e.to_string())?;
        let existing_profiles = profile_manager.list_profiles();

        // Create a map of app identifiers to profile info
        let mut map: HashMap<String, (String, String)> = HashMap::new();
        for profile in existing_profiles {
            map.insert(
                profile.app_identifier.clone(),
                (profile.id.clone(), profile.name.clone())
            );
        }
        map
    };

    // Use real application detection
    #[cfg(target_os = "macos")]
    {
        get_running_applications_macos(app_to_profile).await
    }

    #[cfg(target_os = "windows")]
    {
        get_running_applications_windows(app_to_profile).await
    }

    #[cfg(not(any(target_os = "macos", target_os = "windows")))]
    {
        // Fallback to mock data for unsupported platforms
        get_mock_running_applications(app_to_profile).await
    }
}

#[cfg(target_os = "macos")]
async fn get_running_applications_macos(app_to_profile: HashMap<String, (String, String)>) -> Result<Vec<serde_json::Value>, String> {
    use std::process::Command;

    // Use AppleScript to get running applications
    let script = r#"
        tell application "System Events"
            set appList to {}
            repeat with proc in (every application process whose background only is false)
                try
                    set appName to name of proc
                    set appBundle to bundle identifier of proc
                    set windowCount to count of windows of proc
                    set end of appList to appName & "|" & appBundle & "|" & (windowCount as string)
                end try
            end repeat
            return appList
        end tell
    "#;

    let output = Command::new("osascript")
        .arg("-e")
        .arg(script)
        .output()
        .map_err(|e| format!("Failed to execute AppleScript: {}", e))?;

    if !output.status.success() {
        let error = String::from_utf8_lossy(&output.stderr);
        return Err(format!("AppleScript error: {}", error));
    }

    let result = String::from_utf8_lossy(&output.stdout);
    let mut applications = Vec::new();
    let mut seen_apps: HashMap<String, u32> = HashMap::new();

    for line in result.lines() {
        if line.trim().is_empty() {
            continue;
        }

        let parts: Vec<&str> = line.split('|').collect();
        if parts.len() != 3 {
            continue;
        }

        let app_name = parts[0].trim().to_string();
        let bundle_id = parts[1].trim().to_string();
        let window_count: u32 = parts[2].trim().parse().unwrap_or(0);

        // Skip system apps and duplicates
        if bundle_id.is_empty() || bundle_id.starts_with("com.apple.") &&
           !bundle_id.contains("TextEdit") && !bundle_id.contains("Terminal") {
            continue;
        }

        // Aggregate window counts for same app
        if let Some(existing_count) = seen_apps.get_mut(&bundle_id) {
            *existing_count += window_count;
            continue;
        }
        seen_apps.insert(bundle_id.clone(), window_count);

        // Check if app has an assigned profile
        let (has_profile, profile_id) = if let Some((id, _name)) = app_to_profile.get(&bundle_id) {
            (true, Some(id.clone()))
        } else {
            (false, None)
        };

        // Get app icon path
        let icon_path = get_app_icon_path_macos(&bundle_id).await.ok();

        applications.push(serde_json::json!({
            "app_identifier": bundle_id,
            "app_name": app_name,
            "icon_path": icon_path,
            "window_count": window_count,
            "has_profile": has_profile,
            "profile_id": profile_id
        }));
    }

    // Sort by app name for consistent display
    applications.sort_by(|a, b| {
        a["app_name"].as_str().unwrap_or("")
            .cmp(b["app_name"].as_str().unwrap_or(""))
    });

    Ok(applications)
}

#[cfg(target_os = "windows")]
async fn get_running_applications_windows(_app_to_profile: HashMap<String, (String, String)>) -> Result<Vec<serde_json::Value>, String> {
    // TODO: Implement Windows application detection
    // For now, return empty list
    Ok(vec![])
}



#[tauri::command]
async fn assign_profile_to_app(
    app_identifier: String,
    profile_id: String,
    profile_manager_state: State<'_, ProfileManagerState>,
) -> Result<(), String> {
    let mut profile_manager = profile_manager_state.lock().map_err(|e| e.to_string())?;

    // Get the profile to verify it exists
    let profile = profile_manager.get_profile(&profile_id)
        .ok_or("Profile not found")?
        .clone();

    // Update the profile's app_identifier
    let mut updated_profile = profile;
    updated_profile.app_identifier = app_identifier.clone();
    updated_profile.updated_at = chrono::Utc::now();

    // Save the updated profile
    profile_manager.update_profile(updated_profile)
        .map_err(|e| format!("Failed to assign profile: {}", e))?;

    log::info!("Successfully assigned profile '{}' to app '{}'", profile_id, app_identifier);
    Ok(())
}

#[tauri::command]
async fn get_app_icon_path(app_identifier: String) -> Result<Option<String>, String> {
    #[cfg(target_os = "macos")]
    {
        get_app_icon_path_macos(&app_identifier).await
    }

    #[cfg(target_os = "windows")]
    {
        get_app_icon_path_windows(&app_identifier).await
    }

    #[cfg(not(any(target_os = "macos", target_os = "windows")))]
    {
        Ok(None)
    }
}

#[cfg(target_os = "macos")]
async fn get_app_icon_path_macos(app_identifier: &str) -> Result<Option<String>, String> {
    use std::process::Command;
    use std::path::Path;

    // Use mdfind to locate the app bundle
    let output = Command::new("mdfind")
        .arg(format!("kMDItemCFBundleIdentifier == '{}'", app_identifier))
        .output()
        .map_err(|e| format!("Failed to execute mdfind: {}", e))?;

    if !output.status.success() {
        return Ok(None);
    }

    let result = String::from_utf8_lossy(&output.stdout);
    let app_path = result.lines().next();

    if let Some(app_path) = app_path {
        let app_path = app_path.trim();
        if Path::new(app_path).exists() {
            // Try to find the app icon
            let icon_path = format!("{}/Contents/Resources/AppIcon.icns", app_path);
            if Path::new(&icon_path).exists() {
                return Ok(Some(icon_path));
            }

            // Try alternative icon names
            let alternative_icons = ["icon.icns", "app.icns", "application.icns"];
            for icon_name in &alternative_icons {
                let icon_path = format!("{}/Contents/Resources/{}", app_path, icon_name);
                if Path::new(&icon_path).exists() {
                    return Ok(Some(icon_path));
                }
            }
        }
    }

    Ok(None)
}

#[cfg(target_os = "windows")]
async fn get_app_icon_path_windows(_app_identifier: &str) -> Result<Option<String>, String> {
    // TODO: Implement Windows icon extraction
    Ok(None)
}

#[tauri::command]
async fn export_profiles(
    profile_manager_state: State<'_, ProfileManagerState>,
) -> Result<Vec<ApplicationProfile>, String> {
    let profile_manager = profile_manager_state.lock().map_err(|e| e.to_string())?;
    let profiles = profile_manager.list_profiles()
        .into_iter()
        .cloned()
        .collect();
    Ok(profiles)
}

#[tauri::command]
async fn import_profiles(
    profiles: Vec<ApplicationProfile>,
    profile_manager_state: State<'_, ProfileManagerState>,
) -> Result<ImportResult, String> {
    let mut profile_manager = profile_manager_state.lock().map_err(|e| e.to_string())?;

    let mut result = ImportResult {
        total_profiles: profiles.len(),
        successful_imports: 0,
        failed_imports: 0,
        skipped_profiles: 0,
        errors: Vec::new(),
    };

    for profile in profiles {
        // Validate profile before importing
        match validate_profile_for_import(&profile, &profile_manager) {
            Ok(validated_profile) => {
                match profile_manager.add_profile(validated_profile) {
                    Ok(_) => {
                        result.successful_imports += 1;
                        log::info!("Successfully imported profile: {}", profile.name);
                    }
                    Err(e) => {
                        result.failed_imports += 1;
                        let error_msg = format!("Failed to import profile '{}': {}", profile.name, e);
                        result.errors.push(error_msg);
                        log::warn!("Failed to import profile '{}': {}", profile.name, e);
                    }
                }
            }
            Err(ValidationAction::Skip(reason)) => {
                result.skipped_profiles += 1;
                let skip_msg = format!("Skipped profile '{}': {}", profile.name, reason);
                result.errors.push(skip_msg);
                log::info!("Skipped profile '{}': {}", profile.name, reason);
            }
            Err(ValidationAction::Error(reason)) => {
                result.failed_imports += 1;
                let error_msg = format!("Invalid profile '{}': {}", profile.name, reason);
                result.errors.push(error_msg);
                log::warn!("Invalid profile '{}': {}", profile.name, reason);
            }
        }
    }

    log::info!("Import completed: {} successful, {} failed, {} skipped",
               result.successful_imports, result.failed_imports, result.skipped_profiles);

    Ok(result)
}

#[tauri::command]
async fn export_profiles_to_file(
    file_path: String,
    profile_manager_state: State<'_, ProfileManagerState>,
) -> Result<ExportResult, String> {
    use std::fs;

    let profile_manager = profile_manager_state.lock().map_err(|e| e.to_string())?;
    let profiles = profile_manager.list_profiles();

    // Create export data with metadata
    let export_data = ProfileExportData {
        version: "1.0".to_string(),
        exported_at: chrono::Utc::now(),
        total_profiles: profiles.len(),
        profiles: profiles.into_iter().cloned().collect(),
    };

    // Serialize to JSON with pretty formatting
    let json_data = serde_json::to_string_pretty(&export_data)
        .map_err(|e| format!("Failed to serialize profiles: {}", e))?;

    let file_size_bytes = json_data.len();

    // Write to file
    fs::write(&file_path, json_data)
        .map_err(|e| format!("Failed to write to file '{}': {}", file_path, e))?;

    let result = ExportResult {
        file_path: file_path.clone(),
        total_profiles: export_data.total_profiles,
        file_size_bytes,
        exported_at: export_data.exported_at,
    };

    log::info!("Successfully exported {} profiles to '{}'", result.total_profiles, file_path);
    Ok(result)
}

#[tauri::command]
async fn import_profiles_from_file(
    file_path: String,
    profile_manager_state: State<'_, ProfileManagerState>,
) -> Result<ImportResult, String> {
    use std::fs;

    // Read file
    let file_content = fs::read_to_string(&file_path)
        .map_err(|e| format!("Failed to read file '{}': {}", file_path, e))?;

    // Try to parse as ProfileExportData first (new format)
    let profiles = match serde_json::from_str::<ProfileExportData>(&file_content) {
        Ok(export_data) => {
            log::info!("Importing from new format: {} profiles from {}",
                      export_data.total_profiles, export_data.exported_at);
            export_data.profiles
        }
        Err(_) => {
            // Fallback to direct profile array (legacy format)
            serde_json::from_str::<Vec<ApplicationProfile>>(&file_content)
                .map_err(|e| format!("Failed to parse profiles from file: {}", e))?
        }
    };

    log::info!("Parsed {} profiles from file '{}'", profiles.len(), file_path);

    // Use the existing import_profiles function
    import_profiles(profiles, profile_manager_state).await
}

// Supporting data structures for import/export

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProfileExportData {
    pub version: String,
    pub exported_at: DateTime<Utc>,
    pub total_profiles: usize,
    pub profiles: Vec<ApplicationProfile>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ImportResult {
    pub total_profiles: usize,
    pub successful_imports: usize,
    pub failed_imports: usize,
    pub skipped_profiles: usize,
    pub errors: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExportResult {
    pub file_path: String,
    pub total_profiles: usize,
    pub file_size_bytes: usize,
    pub exported_at: DateTime<Utc>,
}

#[derive(Debug)]
enum ValidationAction {
    Skip(String),
    Error(String),
}

fn validate_profile_for_import(
    profile: &ApplicationProfile,
    profile_manager: &features::profile_manager::PersistentProfileManager,
) -> Result<ApplicationProfile, ValidationAction> {
    // Check for empty or invalid name
    if profile.name.trim().is_empty() {
        return Err(ValidationAction::Error("Profile name cannot be empty".to_string()));
    }

    // Check name length
    if profile.name.len() > 100 {
        return Err(ValidationAction::Error("Profile name too long (max 100 characters)".to_string()));
    }

    // Check for duplicate names
    if profile_manager.list_profiles().iter().any(|p| p.name == profile.name) {
        return Err(ValidationAction::Skip(format!("Profile with name '{}' already exists", profile.name)));
    }

    // Validate app identifier format
    if !profile.app_identifier.is_empty() {
        if profile.app_identifier.len() > 200 {
            return Err(ValidationAction::Error("App identifier too long (max 200 characters)".to_string()));
        }

        // Basic bundle ID format validation for macOS
        if profile.app_identifier.contains('.') && !profile.app_identifier.starts_with("com.") {
            log::warn!("App identifier '{}' doesn't follow standard bundle ID format", profile.app_identifier);
        }
    }

    // Validate window class if present
    if let Some(ref window_class) = profile.window_class {
        if window_class.trim().is_empty() {
            return Err(ValidationAction::Error("Window class cannot be empty if specified".to_string()));
        }
        if window_class.len() > 100 {
            return Err(ValidationAction::Error("Window class too long (max 100 characters)".to_string()));
        }
    }

    // Create a new profile with a unique ID and current timestamps
    let mut validated_profile = profile.clone();
    validated_profile.id = uuid::Uuid::new_v4().to_string();
    validated_profile.created_at = Utc::now();
    validated_profile.updated_at = Utc::now();

    Ok(validated_profile)
}

// Encoding-related commands
#[tauri::command]
async fn convert_text_encoding(
    text: String,
    from_encoding: u8,
    to_encoding: u8,
) -> Result<String, String> {
    use crate::engine::{Encoding, EncodingConverter};

    // Validate input text
    if text.is_empty() {
        return Err("Input text cannot be empty".to_string());
    }

    let from = match from_encoding {
        0 => Encoding::Unicode,
        1 => Encoding::Tcvn3,
        2 => Encoding::VniWindows,
        3 => Encoding::UnicodeCompound,
        4 => Encoding::VietnameseLocale,
        _ => return Err(format!("Invalid source encoding ID: {}", from_encoding)),
    };

    let to = match to_encoding {
        0 => Encoding::Unicode,
        1 => Encoding::Tcvn3,
        2 => Encoding::VniWindows,
        3 => Encoding::UnicodeCompound,
        4 => Encoding::VietnameseLocale,
        _ => return Err(format!("Invalid target encoding ID: {}", to_encoding)),
    };

    let converter = EncodingConverter::new();

    // Validate source encoding compatibility
    if !converter.validate_encoding(&text, from) {
        return Err(format!(
            "Text contains characters not supported by source encoding: {}",
            EncodingConverter::get_encoding_name(from)
        ));
    }

    let result = converter.convert_text(&text, from, to);

    // Validate conversion result
    if result.is_empty() && !text.is_empty() {
        return Err("Encoding conversion failed: result is empty".to_string());
    }

    Ok(result)
}

#[tauri::command]
async fn validate_text_encoding(text: String, encoding: u8) -> Result<bool, String> {
    use crate::engine::{Encoding, EncodingConverter};

    // Handle empty text
    if text.is_empty() {
        return Ok(true); // Empty text is valid for any encoding
    }

    let enc = match encoding {
        0 => Encoding::Unicode,
        1 => Encoding::Tcvn3,
        2 => Encoding::VniWindows,
        3 => Encoding::UnicodeCompound,
        4 => Encoding::VietnameseLocale,
        _ => return Err(format!("Invalid encoding ID: {}", encoding)),
    };

    let converter = EncodingConverter::new();
    let is_valid = converter.validate_encoding(&text, enc);

    // Log validation details for debugging
    if !is_valid {
        log::debug!(
            "Text validation failed for encoding {}: '{}'",
            EncodingConverter::get_encoding_name(enc),
            text
        );
    }

    Ok(is_valid)
}

#[tauri::command]
async fn get_supported_encodings() -> Result<Vec<(u8, String)>, String> {
    use crate::engine::{Encoding, EncodingConverter};

    let encodings = EncodingConverter::get_supported_encodings();
    let result = encodings
        .into_iter()
        .map(|(enc, name)| {
            let id = match enc {
                Encoding::Unicode => 0,
                Encoding::Tcvn3 => 1,
                Encoding::VniWindows => 2,
                Encoding::UnicodeCompound => 3,
                Encoding::VietnameseLocale => 4,
            };
            (id, name.to_string())
        })
        .collect();

    Ok(result)
}

#[tauri::command]
async fn get_encoding_name(encoding: u8) -> Result<String, String> {
    use crate::engine::{Encoding, EncodingConverter};

    let enc = match encoding {
        0 => Encoding::Unicode,
        1 => Encoding::Tcvn3,
        2 => Encoding::VniWindows,
        3 => Encoding::UnicodeCompound,
        4 => Encoding::VietnameseLocale,
        _ => return Err(format!("Invalid encoding ID: {}", encoding)),
    };

    Ok(EncodingConverter::get_encoding_name(enc).to_string())
}

#[tauri::command]
async fn detect_text_encoding(text: String) -> Result<Vec<u8>, String> {
    use crate::engine::{Encoding, EncodingConverter};

    if text.is_empty() {
        return Ok(vec![0]); // Default to Unicode for empty text
    }

    let converter = EncodingConverter::new();
    let encodings = [
        Encoding::Unicode,
        Encoding::Tcvn3,
        Encoding::VniWindows,
        Encoding::UnicodeCompound,
        Encoding::VietnameseLocale,
    ];

    let mut compatible_encodings = Vec::new();

    for encoding in encodings.iter() {
        if converter.validate_encoding(&text, *encoding) {
            let encoding_id = match encoding {
                Encoding::Unicode => 0,
                Encoding::Tcvn3 => 1,
                Encoding::VniWindows => 2,
                Encoding::UnicodeCompound => 3,
                Encoding::VietnameseLocale => 4,
            };
            compatible_encodings.push(encoding_id);
        }
    }

    // If no encodings are compatible, suggest Unicode as fallback
    if compatible_encodings.is_empty() {
        compatible_encodings.push(0);
    }

    Ok(compatible_encodings)
}

#[tauri::command]
async fn get_encoding_conversion_preview(
    text: String,
    from_encoding: u8,
    to_encoding: u8,
    max_length: Option<usize>,
) -> Result<String, String> {
    if text.is_empty() {
        return Ok(String::new());
    }

    // Limit preview length to prevent UI issues
    let preview_text = if let Some(max_len) = max_length {
        if text.len() > max_len {
            format!("{}...", &text[..max_len])
        } else {
            text
        }
    } else {
        text
    };

    // Use the existing convert_text_encoding function
    convert_text_encoding(preview_text, from_encoding, to_encoding).await
}

// Debug and testing commands
#[tauri::command]
async fn run_vietnamese_engine_tests() -> Result<Vec<TestResult>, String> {
    log::info!("🧪 [TESTS] Running Vietnamese engine test suite");

    let test_suite = VietnameseEngineTestSuite::new();
    let results = test_suite.run_all_tests();

    log::info!("🧪 [TESTS] Test suite completed: {} tests", results.len());
    for result in &results {
        if result.is_success() {
            log::info!("✅ [TESTS] {}", result.summary());
        } else {
            log::error!("❌ [TESTS] {}", result.summary());
        }
    }

    Ok(results)
}

#[tauri::command]
async fn run_system_diagnostics() -> Result<DiagnosticReport, String> {
    log::info!("🔍 [DIAGNOSTICS] Running comprehensive system diagnostics");

    let diagnostics = VietnameseInputDiagnostics::new();
    let report = diagnostics.run_full_diagnostic();

    log::info!("🔍 [DIAGNOSTICS] System diagnostic completed");
    log::info!("📊 [DIAGNOSTICS] {}", report.summary());

    if !report.get_all_issues().is_empty() {
        log::warn!("⚠️  [DIAGNOSTICS] Issues found:");
        for issue in report.get_all_issues() {
            log::warn!("  • {}", issue);
        }
    }

    Ok(report)
}

#[tauri::command]
async fn get_debug_events(count: usize) -> Result<Vec<crate::debug::DebugEvent>, String> {
    log::debug!("🔍 [DEBUG] Retrieving {} recent debug events", count);

    if let Ok(debugger) = get_global_debugger().lock() {
        let events = debugger.get_recent_events(count);
        log::debug!("🔍 [DEBUG] Retrieved {} debug events", events.len());
        Ok(events)
    } else {
        log::error!("❌ [DEBUG] Failed to lock global debugger");
        Err("Failed to access debug events".to_string())
    }
}

#[tauri::command]
async fn clear_debug_events() -> Result<String, String> {
    log::info!("🧹 [DEBUG] Clearing all debug events");

    if let Ok(debugger) = get_global_debugger().lock() {
        debugger.clear_events();
        log::info!("✅ [DEBUG] Debug events cleared");
        Ok("Debug events cleared successfully".to_string())
    } else {
        log::error!("❌ [DEBUG] Failed to lock global debugger");
        Err("Failed to clear debug events".to_string())
    }
}

#[tauri::command]
#[allow(unused_assignments)]
async fn test_vietnamese_processing_detailed(
    input: String,
    method: u8,
    encoding: u8,
) -> Result<serde_json::Value, String> {
    log::info!("🧪 [DETAILED_TEST] Testing Vietnamese processing: '{}' with method: {} encoding: {}", input, method, encoding);

    // Log to global debugger
    if let Ok(debugger) = get_global_debugger().lock() {
        debugger.log_event(
            DebugEventType::VietnameseProcessing,
            "DetailedTest",
            &format!("Testing input: '{}' with method: {} encoding: {}", input, method, encoding),
            Some(serde_json::json!({
                "input": input,
                "method": method,
                "encoding": encoding
            })),
            DebugLevel::Info,
        );
    }

    let mut processor = engine::vietnamese::VietnameseProcessor::new();

    // Set input method
    processor.input_method = match method {
        0 => engine::InputMethod::Telex,
        1 => engine::InputMethod::VNI,
        2 => engine::InputMethod::SimpleTelex1,
        3 => engine::InputMethod::SimpleTelex2,
        _ => engine::InputMethod::Telex,
    };

    // Set encoding
    processor.encoding = match encoding {
        0 => engine::Encoding::Unicode,
        1 => engine::Encoding::Tcvn3,
        2 => engine::Encoding::VniWindows,
        _ => engine::Encoding::Unicode,
    };

    processor.language_mode = engine::LanguageMode::Vietnamese;

    // Process character by character to get detailed results
    let mut detailed_results = Vec::new();

    for (i, ch) in input.chars().enumerate() {
        let result = processor.process_character(ch);
        let current_buffer = processor.get_buffer_content();

        let step_result = serde_json::json!({
            "step": i + 1,
            "character": ch,
            "buffer_after": current_buffer,
            "process_result": format!("{:?}", result),
            "result_type": match result {
                engine::traits::ProcessResult::NoChange => "no_change",
                engine::traits::ProcessResult::Replace { .. } => "replace",
                engine::traits::ProcessResult::ReplaceText { .. } => "replace_text",
                engine::traits::ProcessResult::Append { .. } => "append",
                engine::traits::ProcessResult::Delete { .. } => "delete",
                engine::traits::ProcessResult::ReplaceAndAppend { .. } => "replace_and_append",
                engine::traits::ProcessResult::Macro { .. } => "macro",
                engine::traits::ProcessResult::StateRestore { .. } => "state_restore",
                engine::traits::ProcessResult::LanguageToggle { .. } => "language_toggle",
                engine::traits::ProcessResult::StateChange { .. } => "state_change",
                engine::traits::ProcessResult::Error { .. } => "error",
                engine::traits::ProcessResult::PassThrough => "pass_through",
            }
        });

        detailed_results.push(step_result);
    }

    // Get final processed result
    let chars: Vec<char> = input.chars().collect();
    let final_result = processor.process_with_spell_check(&chars);

    let response = serde_json::json!({
        "input": input,
        "final_result": final_result,
        "method": format!("{:?}", processor.input_method),
        "encoding": format!("{:?}", processor.encoding),
        "steps": detailed_results,
        "buffer_final": processor.get_buffer_content(),
        "success": true
    });

    log::info!("🧪 [DETAILED_TEST] Processing completed: '{}' -> '{}'", input, final_result);

    Ok(response)
}

// System tray commands

/// Public function for handling tray events (callable from other modules)
pub async fn handle_tray_event_internal(
    event: String,
    state: tauri::State<'_, AppState>,
    app_handle: tauri::AppHandle,
) -> Result<(), String> {
    handle_tray_event_impl(event, state, app_handle).await
}

#[tauri::command]
async fn handle_tray_event(
    event: String,
    state: State<'_, AppState>,
    app_handle: tauri::AppHandle,
) -> Result<(), String> {
    handle_tray_event_impl(event, state, app_handle).await
}

/// Internal implementation of tray event handling
async fn handle_tray_event_impl(
    event: String,
    state: tauri::State<'_, AppState>,
    app_handle: tauri::AppHandle,
) -> Result<(), String> {
    log::info!("🖱️ Tray event received: {}", event);

    match event.as_str() {
        "show" => {
            if let Some(window) = app_handle.get_webview_window("main") {
                window.show().map_err(|e| e.to_string())?;
                window.set_focus().map_err(|e| e.to_string())?;
            }
        }
        "hide" => {
            if let Some(window) = app_handle.get_webview_window("main") {
                window.hide().map_err(|e| e.to_string())?;
            }
        }
        "status" => {
            // Show current status
            if let Some(window) = app_handle.get_webview_window("main") {
                window.show().map_err(|e| e.to_string())?;
                window.set_focus().map_err(|e| e.to_string())?;
            }
        }
        "toggle_vietnamese" => {
            // Toggle Vietnamese/English mode
            log::info!("🔄 Toggling Vietnamese/English mode");

            // Get current settings and toggle language mode
            let config_state = app_handle.state::<ConfigState>();
            let is_vietnamese = {
                let mut config = config_state.lock().unwrap();
                config.language_mode = match config.language_mode {
                    crate::engine::LanguageMode::Vietnamese => crate::engine::LanguageMode::English,
                    crate::engine::LanguageMode::English => crate::engine::LanguageMode::Vietnamese,
                };
                matches!(config.language_mode, crate::engine::LanguageMode::Vietnamese)
            };

            // Start or stop keyboard capture based on the new mode
            #[cfg(target_os = "macos")]
            {
                let macos_state = app_handle.state::<MacOSState>();
                if is_vietnamese {
                    // Starting Vietnamese mode - start keyboard capture
                    log::info!("🚀 [TRAY] Starting Vietnamese input capture due to mode toggle");
                    match macos_state.lock() {
                        Ok(macos) => {
                            if let Err(e) = macos.start_global_event_capture() {
                                log::error!("❌ [TRAY] Failed to start capture when switching to Vietnamese: {}", e);
                                // Continue anyway - user can manually start via UI if needed
                            } else {
                                log::info!("✅ [TRAY] Vietnamese input capture started successfully via tray toggle");
                            }
                        }
                        Err(e) => {
                            log::error!("❌ [TRAY] Failed to lock macOS state when starting capture: {}", e);
                        }
                    }
                } else {
                    // Switching to English mode - stop keyboard capture
                    log::info!("🛑 [TRAY] Stopping Vietnamese input capture due to mode toggle");
                    match macos_state.lock() {
                        Ok(macos) => {
                            macos.stop_global_event_capture();
                            log::info!("✅ [TRAY] Vietnamese input capture stopped via tray toggle");
                        }
                        Err(e) => {
                            log::error!("❌ [TRAY] Failed to lock macOS state when stopping capture: {}", e);
                        }
                    }
                }
            }

            // Update tray status
            update_tray_status(app_handle.clone(), is_vietnamese).await?;

            // Show notification
            let (title, message) = crate::tray::create_language_change_notification(is_vietnamese);
            show_notification(app_handle.clone(), title, message).await?;
        }
        "toggle_status" => {
            // Toggle OpenKey running status
            log::info!("🔄 Toggle OpenKey running status");

            // Show notification
            let (title, message) = ("OpenKey".to_string(), "Status toggled".to_string());
            show_notification(app_handle.clone(), title, message).await?;
        }
        "toggle_spell_check" => {
            let language_mode = {
                let mut app_state = state.lock().map_err(|e| e.to_string())?;
                app_state.check_spelling = !app_state.check_spelling;
                app_state.language_mode
            };

            // Update tray menu
            update_tray_status(app_handle, language_mode).await?;
        }
        "toggle_smart_switch" => {
            let language_mode = {
                let mut app_state = state.lock().map_err(|e| e.to_string())?;
                app_state.smart_switch_key = !app_state.smart_switch_key;
                app_state.language_mode
            };

            // Update tray menu
            update_tray_status(app_handle, language_mode).await?;
        }
        "toggle_macro" => {
            let language_mode = {
                let mut app_state = state.lock().map_err(|e| e.to_string())?;
                app_state.use_macro = !app_state.use_macro;
                app_state.language_mode
            };

            // Update tray menu
            update_tray_status(app_handle, language_mode).await?;
        }
        "toggle_language" => {
            let language_mode = {
                let mut app_state = state.lock().map_err(|e| e.to_string())?;
                app_state.language_mode = !app_state.language_mode;
                app_state.language_mode
            };

            // Update tray icon and show notification
            update_tray_status(app_handle.clone(), language_mode).await?;
            show_language_notification(app_handle.clone(), language_mode).await?;
        }
        // Input method selection
        "input_telex" => {
            {
                let mut app_state = state.lock().map_err(|e| e.to_string())?;
                app_state.input_method = 0; // Telex
            }

            // Show notification
            let (title, message) = crate::tray::create_input_method_notification(0);
            show_notification(app_handle.clone(), title, message).await?;
        }
        "input_vni" => {
            {
                let mut app_state = state.lock().map_err(|e| e.to_string())?;
                app_state.input_method = 1; // VNI
            }

            // Show notification
            let (title, message) = crate::tray::create_input_method_notification(1);
            show_notification(app_handle.clone(), title, message).await?;
        }
        "input_simple_telex" => {
            {
                let mut app_state = state.lock().map_err(|e| e.to_string())?;
                app_state.input_method = 2; // Simple Telex
            }

            // Show notification
            let (title, message) = crate::tray::create_input_method_notification(2);
            show_notification(app_handle.clone(), title, message).await?;
        }
        // Encoding selection
        "encoding_unicode" => {
            let mut app_state = state.lock().map_err(|e| e.to_string())?;
            app_state.encoding = 0; // Unicode
        }
        "encoding_tcvn3" => {
            let mut app_state = state.lock().map_err(|e| e.to_string())?;
            app_state.encoding = 1; // TCVN3
        }
        "encoding_vni_windows" => {
            let mut app_state = state.lock().map_err(|e| e.to_string())?;
            app_state.encoding = 2; // VNI Windows
        }
        "settings" => {
            if let Some(window) = app_handle.get_webview_window("main") {
                window.show().map_err(|e| e.to_string())?;
                window.set_focus().map_err(|e| e.to_string())?;
            }
        }
        "macro_editor" => {
            // Open macro management (could open a specific tab or window)
            if let Some(window) = app_handle.get_webview_window("main") {
                window.show().map_err(|e| e.to_string())?;
                window.set_focus().map_err(|e| e.to_string())?;
            }
        }
        "convert_tool" => {
            // Open convert tool (could open a specific window or tab)
            if let Some(window) = app_handle.get_webview_window("main") {
                window.show().map_err(|e| e.to_string())?;
                window.set_focus().map_err(|e| e.to_string())?;
            }
        }
        "quick_convert" => {
            // Open quick convert tool
            if let Some(window) = app_handle.get_webview_window("main") {
                window.show().map_err(|e| e.to_string())?;
                window.set_focus().map_err(|e| e.to_string())?;
            }
        }
        "about" => {
            // Show about dialog
            show_about_dialog(app_handle.clone()).await?;
        }
        "quit" => {
            app_handle.exit(0);
        }
        _ => {
            return Err(format!("Unknown tray event: {}", event));
        }
    }
    Ok(())
}

#[tauri::command]
async fn update_tray_status(
    app_handle: tauri::AppHandle,
    is_vietnamese: bool,
) -> Result<(), String> {
    // For now, just log the update - full implementation will come later
    log::info!(
        "Tray status update requested: Vietnamese = {}",
        is_vietnamese
    );

    // Update tooltip
    let tooltip = if is_vietnamese {
        "OpenKey - Chế độ tiếng Việt"
    } else {
        "OpenKey - English mode"
    };

    // Try to update tray tooltip if available
    if let Some(tray) = app_handle.tray_by_id("main") {
        if let Err(e) = tray.set_tooltip(Some(tooltip)) {
            log::warn!("Failed to update tray tooltip: {}", e);
        }
    }

    Ok(())
}

#[tauri::command]
async fn show_notification(
    _app_handle: tauri::AppHandle,
    title: String,
    message: String,
) -> Result<(), String> {
    // For now, just log the notification
    // This will be enhanced when we properly integrate the notification plugin
    log::info!("Notification: {} - {}", title, message);
    Ok(())
}

#[tauri::command]
async fn show_language_notification(
    _app_handle: tauri::AppHandle,
    is_vietnamese: bool,
) -> Result<(), String> {
    use crate::tray::create_language_change_notification;

    let (title, body) = create_language_change_notification(is_vietnamese);

    // Show notification using tauri-plugin-notification
    // This will be implemented when we integrate the notification plugin
    log::info!("Language changed: {} - {}", title, body);

    Ok(())
}

#[tauri::command]
async fn show_about_dialog(_app_handle: tauri::AppHandle) -> Result<(), String> {
    // Show about dialog - for now just log
    log::info!("About OpenKey - Vietnamese Input Method");
    Ok(())
}

// File dialog commands using Tauri dialog plugin
#[tauri::command]
async fn open_macro_file_dialog(_app_handle: tauri::AppHandle) -> Result<Option<String>, String> {
    // Dialog functionality temporarily simplified

    // For now, return a placeholder path since dialog API needs fixing
    let file_path = Some(std::path::PathBuf::from("macros.txt"));

    match file_path {
        Some(path) => Ok(Some(path.to_string_lossy().to_string())),
        None => Ok(None),
    }
}

#[tauri::command]
async fn save_macro_file_dialog(_app_handle: tauri::AppHandle) -> Result<Option<String>, String> {
    // Dialog functionality temporarily simplified

    // For now, return a placeholder path since dialog API needs fixing
    let file_path = Some(std::path::PathBuf::from("OpenKeyMacros.txt"));

    match file_path {
        Some(path) => Ok(Some(path.to_string_lossy().to_string())),
        None => Ok(None),
    }
}

#[tauri::command]
async fn show_macro_message(title: String, message: String, kind: String) -> Result<bool, String> {
    // For now, always return true
    // This will be implemented with proper message dialogs in the UI
    println!("{}: {} ({})", title, message, kind);
    Ok(true)
}

// macOS Integration Commands
#[cfg(target_os = "macos")]
#[tauri::command]
async fn check_accessibility_permission(
    #[cfg(target_os = "macos")] macos_state: State<'_, MacOSState>,
) -> Result<bool, String> {
    log::info!("🔍 Tauri command: check_accessibility_permission called with enhanced diagnostics");

    // Clone the accessibility permission to avoid holding the mutex across await
    let accessibility_permission = {
        let macos = macos_state.lock().map_err(|e| e.to_string())?;
        macos.get_accessibility_permission().clone()
    };

    let result = match accessibility_permission.check_status().await {
        Ok(status) => {
            let is_granted = status.is_granted();
            log::info!("🔐 Enhanced permission check completed - Status: {:?}, Granted: {}", status, is_granted);
            Ok(is_granted)
        }
        Err(e) => {
            log::error!("❌ Enhanced accessibility permission check failed: {}", e);
            Err(e.to_string())
        }
    }?;

    log::info!("🔍 Tauri command result: check_accessibility_permission = {}", result);
    Ok(result)
}

#[cfg(target_os = "macos")]
#[tauri::command]
async fn request_accessibility_permission(
    #[cfg(target_os = "macos")] macos_state: State<'_, MacOSState>,
) -> Result<bool, String> {
    log::info!("🔐 Tauri command: request_accessibility_permission called with enhanced diagnostics");

    // Clone the accessibility permission to avoid holding the mutex across await
    let accessibility_permission = {
        let macos = macos_state.lock().map_err(|e| e.to_string())?;
        macos.get_accessibility_permission().clone()
    };

    let result = match accessibility_permission.request_permission().await {
        Ok(result) => {
            log::info!("🔐 Enhanced permission request completed - Result: {:?}", result);
            Ok(result.is_success())
        }
        Err(e) => {
            log::error!("❌ Enhanced accessibility permission request failed: {}", e);
            Err(e.to_string())
        }
    }?;

    log::info!("🔐 Tauri command result: request_accessibility_permission = {}", result);
    Ok(result)
}

#[cfg(target_os = "macos")]
#[tauri::command]
async fn start_global_capture(
    #[cfg(target_os = "macos")] macos_state: State<'_, MacOSState>,
) -> Result<(), String> {
    log::info!("🚀 [UI_COMMAND] start_global_capture called from frontend");

    log::debug!("🔧 [UI_COMMAND] Attempting to acquire macOS state lock...");
    let macos = macos_state.lock().map_err(|e| {
        let error_msg = format!("Failed to acquire macOS state lock: {}", e);
        log::error!("❌ [UI_COMMAND] {}", error_msg);
        error_msg
    })?;

    log::debug!("✅ [UI_COMMAND] macOS state lock acquired, calling start_global_event_capture()");

    match macos.start_global_event_capture() {
        Ok(()) => {
            log::info!("✅ [UI_COMMAND] Global capture started successfully via UI command");
            Ok(())
        }
        Err(e) => {
            let error_msg = format!("Failed to start global capture: {}", e);
            log::error!("❌ [UI_COMMAND] {}", error_msg);
            Err(error_msg)
        }
    }
}

#[cfg(target_os = "macos")]
#[tauri::command]
async fn stop_global_capture(
    #[cfg(target_os = "macos")] macos_state: State<'_, MacOSState>,
) -> Result<(), String> {
    log::info!("🛑 [UI_COMMAND] stop_global_capture called from frontend");

    log::debug!("🔧 [UI_COMMAND] Attempting to acquire macOS state lock...");
    let macos = macos_state.lock().map_err(|e| {
        let error_msg = format!("Failed to acquire macOS state lock: {}", e);
        log::error!("❌ [UI_COMMAND] {}", error_msg);
        error_msg
    })?;

    log::debug!("✅ [UI_COMMAND] macOS state lock acquired, calling stop_global_event_capture()");
    macos.stop_global_event_capture();
    log::info!("✅ [UI_COMMAND] Global capture stopped successfully via UI command");
    Ok(())
}

#[cfg(target_os = "macos")]
#[tauri::command]
async fn is_global_capture_active(
    #[cfg(target_os = "macos")] macos_state: State<'_, MacOSState>,
) -> Result<bool, String> {
    log::debug!("🔍 [UI_COMMAND] is_global_capture_active called from frontend");

    let macos = macos_state.lock().map_err(|e| {
        let error_msg = format!("Failed to acquire macOS state lock: {}", e);
        log::error!("❌ [UI_COMMAND] {}", error_msg);
        error_msg
    })?;

    let is_active = macos.is_listening();
    log::debug!("📊 [UI_COMMAND] Global capture status: {}", is_active);
    Ok(is_active)
}

#[cfg(target_os = "macos")]
#[tauri::command]
async fn open_accessibility_settings(
    #[cfg(target_os = "macos")] macos_state: State<'_, MacOSState>,
) -> Result<(), String> {
    let macos = macos_state.lock().map_err(|e| e.to_string())?;
    macos.open_accessibility_settings()
}

#[cfg(target_os = "macos")]
#[tauri::command]
async fn request_accessibility_permission_with_dialog(
    #[cfg(target_os = "macos")] macos_state: State<'_, MacOSState>,
    app_handle: tauri::AppHandle,
) -> Result<bool, String> {
    let macos = macos_state.lock().map_err(|e| e.to_string())?;

    // Check if already granted
    if macos.check_accessibility_permission() {
        return Ok(true);
    }

    // Show dialog asking user to grant permission
    use tauri_plugin_dialog::{DialogExt, MessageDialogButtons, MessageDialogKind};

    let response = app_handle.dialog()
        .message("OpenKey needs accessibility permission to capture keyboard input for Vietnamese text processing.\n\nWould you like to open System Preferences to grant this permission?")
        .title("OpenKey Accessibility Permission")
        .kind(MessageDialogKind::Info)
        .buttons(MessageDialogButtons::OkCancel)
        .blocking_show();

    if response {
        // User clicked OK, request permission with system prompt
        let permission_granted = macos.request_accessibility_permission();
        if !permission_granted {
            // Also open system preferences as fallback
            let _ = macos.open_accessibility_settings();
        }
        Ok(permission_granted)
    } else {
        // User clicked Cancel
        Ok(false)
    }
}

#[cfg(target_os = "macos")]
#[tauri::command]
async fn get_accessibility_permission_status(
    #[cfg(target_os = "macos")] macos_state: State<'_, MacOSState>,
) -> Result<bool, String> {
    let macos = macos_state.lock().map_err(|e| e.to_string())?;
    Ok(macos.check_accessibility_permission())
}

#[cfg(target_os = "macos")]
#[tauri::command]
async fn get_permissions_summary(
    #[cfg(target_os = "macos")] _macos_state: State<'_, MacOSState>,
) -> Result<serde_json::Value, String> {
    use macos_permissions::PermissionManager;

    let permission_manager = PermissionManager::new();

    match permission_manager.get_permissions_summary().await {
        Ok(summary) => {
            let json_summary = serde_json::json!({
                "total_count": summary.total_count,
                "granted_count": summary.granted_count,
                "denied_count": summary.denied_count,
                "not_determined_count": summary.not_determined_count,
                "restricted_count": summary.restricted_count,
                "granted_percentage": summary.granted_percentage(),
                "all_granted": summary.all_granted(),
                "has_denied": summary.has_denied(),
                "has_not_determined": summary.has_not_determined(),
                "has_restricted": summary.has_restricted()
            });
            Ok(json_summary)
        }
        Err(e) => Err(format!("Failed to get permissions summary: {}", e))
    }
}

#[cfg(target_os = "macos")]
#[tauri::command]
async fn check_specific_permission(
    permission_type: String,
    #[cfg(target_os = "macos")] _macos_state: State<'_, MacOSState>,
) -> Result<String, String> {
    use macos_permissions::{PermissionManager, PermissionType};

    let permission_manager = PermissionManager::new();

    let perm_type = match permission_type.as_str() {
        "accessibility" => PermissionType::Accessibility,
        "camera" => PermissionType::Camera,
        "microphone" => PermissionType::Microphone,
        "network" => PermissionType::Network,
        "screen_recording" => PermissionType::ScreenRecording,
        "file_system" => PermissionType::FileSystem,
        "full_disk_access" => PermissionType::FullDiskAccess,
        "input_monitoring" => PermissionType::InputMonitoring,
        _ => return Err(format!("Unknown permission type: {}", permission_type))
    };

    match permission_manager.check_permission(perm_type).await {
        Ok(status) => Ok(format!("{:?}", status)),
        Err(e) => Err(format!("Failed to check permission: {}", e))
    }
}

#[tauri::command]
async fn request_specific_permission(
    permission_type: String,
    #[cfg(target_os = "macos")] _macos_state: State<'_, MacOSState>,
) -> Result<String, String> {
    use macos_permissions::{PermissionManager, PermissionType};

    let permission_manager = PermissionManager::new();

    let perm_type = match permission_type.as_str() {
        "accessibility" => PermissionType::Accessibility,
        "camera" => PermissionType::Camera,
        "microphone" => PermissionType::Microphone,
        "network" => PermissionType::Network,
        "screen_recording" => PermissionType::ScreenRecording,
        "file_system" => PermissionType::FileSystem,
        "full_disk_access" => PermissionType::FullDiskAccess,
        "input_monitoring" => PermissionType::InputMonitoring,
        _ => return Err(format!("Unknown permission type: {}", permission_type))
    };

    match permission_manager.request_permission(perm_type).await {
        Ok(result) => Ok(format!("Status: {:?}, User Prompted: {}, Status Changed: {}, Message: {:?}",
                                result.status, result.user_prompted, result.status_changed, result.message)),
        Err(e) => Err(format!("Failed to request permission: {}", e))
    }
}

#[tauri::command]
async fn test_screen_recording_permission(
    #[cfg(target_os = "macos")] _macos_state: State<'_, MacOSState>,
) -> Result<String, String> {
    use macos_permissions::{ScreenRecordingPermission, Permission};

    let screen_recording = ScreenRecordingPermission::new();

    // First check current status
    let initial_status = screen_recording.check_status().await
        .map_err(|e| format!("Failed to check status: {}", e))?;

    println!("🎥 Screen Recording Permission Test");
    println!("Initial status: {:?}", initial_status);

    if initial_status.is_granted() {
        return Ok(format!("Screen recording permission already granted: {:?}", initial_status));
    }

    // Request permission (this should show system dialog)
    println!("🔐 Requesting screen recording permission - this should show system dialog...");
    let result = screen_recording.request_permission().await
        .map_err(|e| format!("Failed to request permission: {}", e))?;

    println!("🔐 Permission request result: {:?}", result);

    // Check final status
    let final_status = screen_recording.check_status().await
        .map_err(|e| format!("Failed to check final status: {}", e))?;

    println!("🔍 Final status: {:?}", final_status);

    Ok(format!("Request completed. Initial: {:?}, Final: {:?}, User Prompted: {}, Status Changed: {}, Message: {:?}",
               initial_status, final_status, result.user_prompted, result.status_changed, result.message))
}

#[tauri::command]
async fn test_raw_accessibility_permission(
    #[cfg(target_os = "macos")] _macos_state: State<'_, MacOSState>,
) -> Result<String, String> {
    use macos_permissions::{AccessibilityPermission, Permission};

    println!("🔍 Testing RAW Accessibility Permission Check");

    let accessibility = AccessibilityPermission::new();

    // Check status multiple times to ensure consistency
    let status1 = accessibility.check_status().await
        .map_err(|e| format!("Failed to check status (1st): {}", e))?;

    let status2 = accessibility.check_status().await
        .map_err(|e| format!("Failed to check status (2nd): {}", e))?;

    println!("🔍 First check: {:?}", status1);
    println!("🔍 Second check: {:?}", status2);

    // Also test the simplified boolean API
    let is_granted = accessibility.check_status().await
        .map(|s| s.is_granted())
        .unwrap_or(false);

    println!("🔍 Boolean check: {}", is_granted);

    // If not granted, offer to request permission
    if !status1.is_granted() {
        println!("❌ Accessibility permission NOT granted");
        Ok(format!("Accessibility permission NOT granted. Status: {:?}, Boolean: {}", status1, is_granted))
    } else {
        println!("✅ Accessibility permission IS granted");
        Ok(format!("Accessibility permission IS granted. Status: {:?}, Boolean: {}", status1, is_granted))
    }
}

#[tauri::command]
async fn force_accessibility_permission_request(
    #[cfg(target_os = "macos")] _macos_state: State<'_, MacOSState>,
) -> Result<String, String> {
    use macos_permissions::{AccessibilityPermission, Permission};

    println!("🔐 FORCING Accessibility Permission Request");

    let accessibility = AccessibilityPermission::new();

    // Force request permission (this should show system dialog)
    println!("🔐 Requesting accessibility permission - this SHOULD show system dialog...");
    let result = accessibility.request_permission().await
        .map_err(|e| format!("Failed to request permission: {}", e))?;

    println!("🔐 Permission request result: {:?}", result);

    // Check final status
    let final_status = accessibility.check_status().await
        .map_err(|e| format!("Failed to check final status: {}", e))?;

    println!("🔍 Final status after request: {:?}", final_status);

    Ok(format!("Force request completed. Result: {:?}, Final Status: {:?}, User Prompted: {}, Status Changed: {}",
               result.status, final_status, result.user_prompted, result.status_changed))
}

#[tauri::command]
async fn test_direct_accessibility_api(
    #[cfg(target_os = "macos")] _macos_state: State<'_, MacOSState>,
) -> Result<String, String> {
    println!("🔍 Testing DIRECT Accessibility API Calls");

    #[cfg(target_os = "macos")]
    {
        use core_foundation::base::TCFType;
        use core_foundation::dictionary::{CFDictionary, CFDictionaryRef};
        use core_foundation::string::{CFString, CFStringRef};
        use core_foundation::boolean::CFBoolean;

        // External C functions from ApplicationServices framework
        #[link(name = "ApplicationServices", kind = "framework")]
        extern "C" {
            fn AXIsProcessTrustedWithOptions(options: CFDictionaryRef) -> bool;
            static kAXTrustedCheckOptionPrompt: CFStringRef;
        }

        unsafe {
            // Test 1: Check without prompt
            println!("🔍 Test 1: AXIsProcessTrustedWithOptions(NULL) - no prompt");
            let result1 = AXIsProcessTrustedWithOptions(std::ptr::null());
            println!("🔍 Result 1 (no prompt): {}", result1);

            // Test 2: Check with prompt disabled explicitly
            println!("🔍 Test 2: AXIsProcessTrustedWithOptions with prompt=false");
            let prompt_key = CFString::wrap_under_get_rule(kAXTrustedCheckOptionPrompt);
            let prompt_value = CFBoolean::false_value();
            let pairs = vec![(prompt_key, prompt_value)];
            let options = CFDictionary::from_CFType_pairs(&pairs);
            let result2 = AXIsProcessTrustedWithOptions(options.as_concrete_TypeRef());
            println!("🔍 Result 2 (prompt=false): {}", result2);

            // Test 3: Try to create an event source (practical test)
            println!("🔍 Test 3: Practical test - CGEventSource creation");
            use core_graphics::event_source::{CGEventSource, CGEventSourceStateID};
            let practical_result = match CGEventSource::new(CGEventSourceStateID::HIDSystemState) {
                Ok(_) => {
                    println!("✅ CGEventSource creation successful");
                    true
                }
                Err(e) => {
                    println!("❌ CGEventSource creation failed: {:?}", e);
                    false
                }
            };

            // Test 4: Try AppleScript accessibility test
            println!("🔍 Test 4: AppleScript accessibility test");
            use std::process::Command;
            let script = r#"tell application "System Events" to get name of first application process whose frontmost is true"#;
            let applescript_result = match Command::new("osascript").arg("-e").arg(script).output() {
                Ok(output) if output.status.success() => {
                    let app_name = String::from_utf8_lossy(&output.stdout).trim().to_string();
                    println!("✅ AppleScript test successful: '{}'", app_name);
                    !app_name.is_empty()
                }
                Ok(output) => {
                    let error = String::from_utf8_lossy(&output.stderr);
                    println!("❌ AppleScript test failed: {}", error);
                    false
                }
                Err(e) => {
                    println!("❌ AppleScript execution failed: {}", e);
                    false
                }
            };

            Ok(format!(
                "Direct API Tests:\n\
                1. AXIsProcessTrustedWithOptions(NULL): {}\n\
                2. AXIsProcessTrustedWithOptions(prompt=false): {}\n\
                3. CGEventSource creation: {}\n\
                4. AppleScript accessibility: {}\n\
                \n\
                CONCLUSION: Accessibility permission is {} ACTUALLY working",
                result1, result2, practical_result, applescript_result,
                if result1 && result2 && practical_result && applescript_result { "✅" } else { "❌ NOT" }
            ))
        }
    }

    #[cfg(not(target_os = "macos"))]
    Ok("Not running on macOS".to_string())
}

#[cfg(target_os = "macos")]
#[tauri::command]
async fn force_request_accessibility_permission(
    #[cfg(target_os = "macos")] _macos_state: State<'_, MacOSState>,
) -> Result<bool, String> {
    let macos = _macos_state.lock().map_err(|e| e.to_string())?;
    Ok(macos.force_request_accessibility_permission())
}

#[cfg(target_os = "macos")]
#[tauri::command]
async fn request_accessibility_permission_openkey_style(
    #[cfg(target_os = "macos")] macos_state: State<'_, MacOSState>,
) -> Result<bool, String> {
    let macos = macos_state.lock().map_err(|e| e.to_string())?;
    Ok(macos.request_accessibility_permission_openkey_style())
}

#[cfg(target_os = "macos")]
#[tauri::command]
async fn show_vietnamese_permission_dialog(
    #[cfg(target_os = "macos")] macos_state: State<'_, MacOSState>,
) -> Result<bool, String> {
    let macos = macos_state.lock().map_err(|e| e.to_string())?;
    Ok(macos.show_vietnamese_permission_dialog())
}

#[cfg(target_os = "macos")]
#[tauri::command]
async fn open_accessibility_settings_enhanced(
    #[cfg(target_os = "macos")] macos_state: State<'_, MacOSState>,
) -> Result<bool, String> {
    let macos = macos_state.lock().map_err(|e| e.to_string())?;
    Ok(macos.open_accessibility_settings_enhanced())
}

#[tauri::command]
async fn run_accessibility_diagnostics() -> Result<String, String> {
    println!("🔍 === COMPREHENSIVE ACCESSIBILITY DIAGNOSTICS ===");

    use macos_permissions::{AccessibilityPermission, Permission};
    let accessibility = AccessibilityPermission::new();
    let mut results = Vec::new();

    // Test 1: Basic permission check
    results.push("=== BASIC PERMISSION CHECK ===".to_string());
    match accessibility.check_status().await {
        Ok(status) => {
            results.push(format!("✅ Permission Status: {:?}", status));
            results.push(format!("✅ Is Granted: {}", status.is_granted()));
        }
        Err(e) => {
            results.push(format!("❌ Permission Check Failed: {}", e));
        }
    }

    // Test 2: Force system dialog test
    results.push("\n=== FORCE SYSTEM DIALOG TEST ===".to_string());
    match accessibility.force_request_system_dialog() {
        Ok(result) => {
            results.push(format!("✅ Force Dialog Result: {:?}", result));
        }
        Err(e) => {
            results.push(format!("❌ Force Dialog Failed: {}", e));
        }
    }

    // Test 3: Vietnamese dialog test
    results.push("\n=== VIETNAMESE DIALOG TEST ===".to_string());
    match accessibility.show_vietnamese_permission_dialog() {
        Ok(user_granted) => {
            results.push(format!("✅ Vietnamese Dialog Result: {}", user_granted));
        }
        Err(e) => {
            results.push(format!("❌ Vietnamese Dialog Failed: {}", e));
        }
    }

    Ok(results.join("\n"))
}

#[tauri::command]
async fn test_input_monitoring_permissions() -> Result<String, String> {
    println!("🔍 === INPUT MONITORING PERMISSION TEST ===");

    let mut results = Vec::new();
    results.push("=== INPUT MONITORING DIAGNOSTICS ===".to_string());

    // Test CGEventSource creation
    results.push("\n--- CGEventSource Test ---".to_string());

    #[cfg(target_os = "macos")]
    {
        use core_graphics::event_source::{CGEventSource, CGEventSourceStateID};

        match CGEventSource::new(CGEventSourceStateID::HIDSystemState) {
            Ok(_) => {
                results.push("✅ CGEventSource creation successful".to_string());
                results.push("📊 Input Monitoring permissions appear to be granted".to_string());
            }
            Err(e) => {
                results.push(format!("❌ CGEventSource creation failed: {:?}", e));
                results.push("📊 Input Monitoring permissions appear to be missing".to_string());
                results.push("🔍 This is separate from Accessibility permissions on macOS 10.15+".to_string());
            }
        }

        // Test CGEventTap creation (disabled due to complexity)
        results.push("\n--- CGEventTap Test ---".to_string());
        results.push("🔧 CGEventTap test disabled due to lifetime complexity".to_string());
        results.push("🔧 CGEventSource test above is the primary indicator for Input Monitoring".to_string());
        results.push("📊 For comprehensive testing, the CGEventSource result is sufficient".to_string());
    }

    #[cfg(not(target_os = "macos"))]
    {
        results.push("❌ Input Monitoring tests only work on macOS".to_string());
    }

    Ok(results.join("\n"))
}

#[tauri::command]
async fn test_practical_accessibility(
    #[cfg(target_os = "macos")] macos_state: State<'_, MacOSState>,
) -> Result<String, String> {
    log::info!("🔍 Tauri command: test_practical_accessibility called with enhanced integration");

    #[cfg(target_os = "macos")]
    {
        let macos = macos_state.lock().map_err(|e| e.to_string())?;
        macos.test_practical_accessibility()
    }

    #[cfg(not(target_os = "macos"))]
    {
        Ok("❌ Practical accessibility tests only work on macOS".to_string())
    }
}

// Configuration management commands
#[tauri::command]
async fn load_config(config_state: State<'_, ConfigState>) -> Result<OpenKeyConfig, String> {
    let config = config_state.lock().map_err(|e| e.to_string())?;
    Ok(config.clone())
}

#[tauri::command]
async fn save_config(
    new_config: OpenKeyConfig,
    config_state: State<'_, ConfigState>,
) -> Result<(), String> {
    // Validate and save the new configuration
    new_config.save().map_err(|e| e.to_string())?;

    // Update the state
    let mut config = config_state.lock().map_err(|e| e.to_string())?;
    *config = new_config;

    log::info!("✅ Configuration updated and saved successfully");
    Ok(())
}

#[tauri::command]
async fn reset_config_to_defaults(
    config_state: State<'_, ConfigState>,
) -> Result<OpenKeyConfig, String> {
    let default_config = OpenKeyConfig::reset_to_defaults().map_err(|e| e.to_string())?;

    // Update the state
    let mut config = config_state.lock().map_err(|e| e.to_string())?;
    *config = default_config.clone();

    log::info!("✅ Configuration reset to defaults");
    Ok(default_config)
}

#[tauri::command]
async fn backup_config(config_state: State<'_, ConfigState>) -> Result<String, String> {
    let config = config_state.lock().map_err(|e| e.to_string())?;
    let backup_path = config.backup().map_err(|e| e.to_string())?;
    Ok(backup_path.to_string_lossy().to_string())
}

#[tauri::command]
async fn restore_config_from_backup(
    config_state: State<'_, ConfigState>,
) -> Result<OpenKeyConfig, String> {
    let restored_config = OpenKeyConfig::restore_from_backup().map_err(|e| e.to_string())?;

    // Update the state
    let mut config = config_state.lock().map_err(|e| e.to_string())?;
    *config = restored_config.clone();

    log::info!("✅ Configuration restored from backup");
    Ok(restored_config)
}

#[cfg(target_os = "macos")]
#[tauri::command]
async fn run_comprehensive_accessibility_diagnostics(
    #[cfg(target_os = "macos")] macos_state: State<'_, MacOSState>,
) -> Result<String, String> {
    log::info!("🔍 Tauri command: run_comprehensive_accessibility_diagnostics called");
    let macos = macos_state.lock().map_err(|e| e.to_string())?;
    macos.run_comprehensive_accessibility_diagnostics()
}

#[cfg(target_os = "macos")]
#[tauri::command]
async fn test_input_monitoring_permissions_integrated(
    #[cfg(target_os = "macos")] macos_state: State<'_, MacOSState>,
) -> Result<String, String> {
    log::info!("🔍 Tauri command: test_input_monitoring_permissions_integrated called");
    let macos = macos_state.lock().map_err(|e| e.to_string())?;
    macos.test_input_monitoring_permissions()
}



#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    // Initialize enhanced logging for comprehensive diagnostics
    // This will capture debug output from the macos-permissions-lib
    env_logger::Builder::from_env(
        env_logger::Env::default().default_filter_or("info,macos_permissions=debug")
    ).init();

    log::info!("🚀 OpenKey Rust starting with enhanced permission diagnostics");

    // Initialize settings - try to load from file, fallback to default
    let settings = SettingsManager::load_settings().unwrap_or_else(|_| {
        println!("Failed to load settings, using defaults");
        OpenKeySettings::default()
    });
    let app_state: AppState = Arc::new(Mutex::new(settings.clone()));

    // Initialize configuration state
    let config = OpenKeyConfig::load();
    let config_state: ConfigState = Arc::new(Mutex::new(config));

    // Initialize Vietnamese engine with settings
    log::info!("🔧 [ENGINE_INIT] Initializing Vietnamese engine...");
    let mut engine = VietnameseEngine::new();
    engine.quick_telex = settings.quick_telex;
    engine.spell_check_enabled = settings.check_spelling;
    engine.modern_orthography = settings.modern_orthography;
    engine.restore_if_wrong = settings.restore_if_invalid;
    engine.fix_browser_recommend = settings.fix_recommend_browser;

    log::info!("🔧 [ENGINE_INIT] Engine configuration:");
    log::info!("   - Language Mode: {:?}", engine.language_mode);
    log::info!("   - Input Method: {:?}", engine.input_method);
    log::info!("   - Encoding: {:?}", engine.encoding);
    log::info!("   - Quick Telex: {}", engine.quick_telex);
    log::info!("   - Spell Check: {}", engine.spell_check_enabled);
    log::info!("   - Modern Orthography: {}", engine.modern_orthography);
    log::info!("   - Restore if Wrong: {}", engine.restore_if_wrong);

    let engine_state: EngineState = Arc::new(Mutex::new(engine));
    log::info!("✅ [ENGINE_INIT] Vietnamese engine initialized successfully");

    // Initialize profile manager
    log::info!("🔧 [PROFILE_INIT] Initializing profile manager...");
    let profile_manager = PersistentProfileManager::new().unwrap_or_else(|e| {
        log::error!("Failed to initialize profile manager: {}", e);
        panic!("Profile manager initialization failed: {}", e);
    });
    let profile_manager_state: ProfileManagerState = Arc::new(Mutex::new(profile_manager));
    log::info!("✅ [PROFILE_INIT] Profile manager initialized successfully");

    // Initialize macOS integration
    #[cfg(target_os = "macos")]
    let macos_state: MacOSState = Arc::new(Mutex::new(MacOSIntegration::new(Arc::clone(&engine_state))));

    let mut builder = tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        .plugin(tauri_plugin_dialog::init())
        .plugin(tauri_plugin_notification::init())
        .plugin(tauri_plugin_store::Builder::default().build())
        .manage(app_state)
        .manage(engine_state.clone())
        .manage(config_state)
        .manage(profile_manager_state);

    #[cfg(target_os = "macos")]
    {
        println!("🔧 Creating and managing MacOSState...");
        builder = builder.manage(macos_state);
    }

    // Capture settings values for use in the setup closure
    let vietnamese_mode_enabled = settings.language_mode;

    builder = builder.setup(move |app| {
            println!("🚀 OpenKey starting up...");

            // Check accessibility permissions on startup (similar to original OpenKey)
            #[cfg(target_os = "macos")]
            {
                println!("🔍 Checking for macOS state...");
                if let Some(macos_state) = app.try_state::<MacOSState>() {
                    println!("✅ macOS state found, checking accessibility permissions...");
                    if let Ok(macos) = macos_state.lock() {
                        println!("🔐 Acquired lock on macOS state, checking accessibility permissions...");

                        // Implement the EXACT original OpenKey permission check flow
                        // Step 1: Check permission WITHOUT prompting (like MJAccessibilityIsEnabled)
                        println!("🔐 Step 1: Checking accessibility permission WITHOUT prompting (like original OpenKey)...");
                        let permission_status = macos.check_accessibility_permission();
                        println!("🔐 Permission status (no prompt): {}", permission_status);

                        if !permission_status {
                            // Step 2: Permission NOT granted - show Vietnamese dialog and request permission
                            println!("❌ Accessibility permission NOT granted!");
                            println!("🇻🇳 Step 2: Following original OpenKey flow - showing Vietnamese dialog...");
                            println!("📱 This will show:");
                            println!("   - Vietnamese permission dialog: 'OpenKey cần bạn cấp quyền để có thể hoạt động!'");
                            println!("   - Then macOS system permission dialog");
                            println!("   - AppleScript fallback for older macOS versions");

                            let system_permission_granted = macos.request_accessibility_permission_openkey_style();
                            println!("🔐 OpenKey-style permission request result: {}", system_permission_granted);

                            if !system_permission_granted {
                                println!("❌ Permission denied - app should terminate (like original OpenKey)");
                                println!("💡 In original OpenKey, app would terminate here and require restart");
                                println!("🔄 For testing purposes, continuing without termination...");
                            } else {
                                println!("✅ Permission granted! App can continue initialization");
                            }
                        } else {
                            println!("✅ Accessibility permission already granted - continuing with app initialization");
                            println!("💡 No system dialog needed (permission already exists)");
                        }

                        // Now check the final permission status
                        let has_permission = macos.check_accessibility_permission();
                        println!("🔍 Final accessibility permission status: {}", has_permission);

                        if !has_permission {
                            println!("❌ Accessibility permission NOT granted!");
                            log::warn!("Accessibility permission not granted. Vietnamese input will not work until permission is granted.");

                            // Check permission status after the system dialog
                            let permission_after_prompt = macos.check_accessibility_permission();
                            println!("🔍 Permission status after system dialog: {}", permission_after_prompt);

                            if !permission_after_prompt {
                                // Only show our dialog if the system dialog didn't result in granted permissions
                                use tauri_plugin_dialog::{DialogExt, MessageDialogButtons, MessageDialogKind};

                                println!("📱 System dialog didn't grant permission, showing explanation dialog...");
                                let response = app.dialog()
                                    .message("OpenKey cần bạn cấp quyền để có thể hoạt động!\n\nOpenKey needs accessibility permission to capture keyboard input for Vietnamese text processing.\n\nClick OK to open System Preferences to manually grant permission.\n\nVui lòng chạy lại ứng dụng sau khi cấp quyền.\nPlease restart the application after granting permission.")
                                    .title("OpenKey - Accessibility Permission Required")
                                    .kind(MessageDialogKind::Warning)
                                    .buttons(MessageDialogButtons::OkCancel)
                                    .blocking_show();

                                println!("👤 User dialog response: {}", response);

                                if response {
                                    println!("✅ User clicked OK, opening System Preferences...");
                                    // Open system preferences
                                    let _ = macos.open_accessibility_settings();

                                    println!("📋 Showing restart instructions...");
                                    // Show instruction to restart
                                    let _ = app.dialog()
                                        .message("Please enable OpenKey in System Preferences > Security & Privacy > Privacy > Accessibility, then restart the application.\n\nLook for 'openkey-rust' or 'OpenKey' in the list and check the box next to it.")
                                        .title("OpenKey - Permission Instructions")
                                        .kind(MessageDialogKind::Info)
                                        .buttons(MessageDialogButtons::Ok)
                                        .blocking_show();
                                } else {
                                    println!("❌ User clicked Cancel");
                                }
                            } else {
                                println!("✅ Permission granted via system dialog!");
                            }

                            // Exit the application like original OpenKey
                            println!("🚪 Exiting application - accessibility permission required");
                            log::info!("Exiting application - accessibility permission required");
                            std::process::exit(0);
                        } else {
                            println!("✅ Accessibility permission granted! Vietnamese input ready.");
                            println!("📋 App Info: Product Name = 'OpenKey', Bundle ID = 'com.openkey.app'");
                            println!("🔍 If you don't see this app in System Preferences > Accessibility, look for:");
                            println!("   - 'OpenKey'");
                            println!("   - 'openkey-rust' (development name)");
                            println!("   - 'com.openkey.app'");
                            println!("🧪 To test Vietnamese input:");
                            println!("   1. Open a text editor");
                            println!("   2. Try typing: 'anh' (should stay as 'anh')");
                            println!("   3. Try typing: 'ahn' (should become 'ành' if working)");
                            println!("   4. Try typing: 'viet' + 'j' (should become 'việt')");
                            log::info!("Accessibility permission granted. Vietnamese input ready.");
                        }
                    } else {
                        println!("❌ Failed to acquire lock on macOS state");
                    }
                } else {
                    println!("❌ macOS state not found - this might be a problem with state initialization");
                }
            }

            #[cfg(not(target_os = "macos"))]
            {
                println!("ℹ️  Not running on macOS, skipping accessibility permission check");
            }

            // Setup system tray after app is initialized
            if let Err(e) = crate::tray::setup_system_tray(app.handle()) {
                log::error!("Failed to setup system tray: {}", e);
            }

            // Auto-start Vietnamese input capture if enabled in settings
            #[cfg(target_os = "macos")]
            {
                let macos_state = app.handle().state::<MacOSState>();

                // Check if Vietnamese mode is enabled in settings
                if vietnamese_mode_enabled {
                    log::info!("🚀 [AUTO_START] Vietnamese mode enabled in settings - starting global capture");

                    match macos_state.lock() {
                        Ok(macos) => {
                            match macos.start_global_event_capture() {
                                Ok(_) => {
                                    log::info!("✅ [AUTO_START] Vietnamese input capture started automatically");
                                    println!("✅ Vietnamese input is now active system-wide!");
                                    println!("🎯 You can now type Vietnamese characters in any application");
                                    println!("💡 Use the system tray menu to toggle Vietnamese/English mode");
                                }
                                Err(e) => {
                                    log::error!("❌ [AUTO_START] Failed to auto-start Vietnamese capture: {}", e);
                                    println!("⚠️  Vietnamese input auto-start failed: {}", e);
                                    println!("🎯 Use the system tray menu or UI to manually start Vietnamese input");
                                }
                            }
                        }
                        Err(e) => {
                            log::error!("❌ [AUTO_START] Failed to lock macOS state: {}", e);
                            println!("⚠️  Vietnamese input auto-start failed - use manual activation");
                        }
                    }
                } else {
                    log::info!("ℹ️  [AUTO_START] Vietnamese mode disabled in settings - not starting capture");
                    println!("ℹ️  Vietnamese input is disabled in settings");
                    println!("🎯 Use the system tray menu to enable Vietnamese mode");
                }
            }

            Ok(())
        });

    builder
        .invoke_handler(tauri::generate_handler![
            greet,
            toggle_language_mode,
            set_input_method,
            set_encoding,
            process_vietnamese_input,
            process_key_event,
            start_vietnamese_input_capture,
            stop_vietnamese_input_capture,
            get_engine_status,
            get_production_performance_summary,
            get_settings,
            set_quick_telex,
            update_setting,
            add_macro,
            remove_macro,
            get_all_macros,
            clear_all_macros,
            set_macro_enabled,
            set_macro_auto_caps,
            save_macros_to_file,
            load_macros_from_file,
            export_macros_json,
            import_macros_json,
            get_macro_count,
            validate_macro,
            test_macro_expansion,
            open_macro_file_dialog,
            save_macro_file_dialog,
            show_macro_message,
            convert_text_encoding,
            validate_text_encoding,
            get_supported_encodings,
            get_encoding_name,
            detect_text_encoding,
            get_encoding_conversion_preview,
            handle_tray_event,
            update_tray_status,
            show_language_notification,
            show_about_dialog,
            show_notification,
            #[cfg(target_os = "macos")]
            check_accessibility_permission,
            #[cfg(target_os = "macos")]
            request_accessibility_permission,
            #[cfg(target_os = "macos")]
            start_global_capture,
            #[cfg(target_os = "macos")]
            stop_global_capture,
            #[cfg(target_os = "macos")]
            is_global_capture_active,
            #[cfg(target_os = "macos")]
            open_accessibility_settings,
            #[cfg(target_os = "macos")]
            request_accessibility_permission_with_dialog,
            #[cfg(target_os = "macos")]
            get_accessibility_permission_status,
            #[cfg(target_os = "macos")]
            get_permissions_summary,
            #[cfg(target_os = "macos")]
            check_specific_permission,
            #[cfg(target_os = "macos")]
            request_specific_permission,
            #[cfg(target_os = "macos")]
            test_screen_recording_permission,
            #[cfg(target_os = "macos")]
            test_raw_accessibility_permission,
            #[cfg(target_os = "macos")]
            force_accessibility_permission_request,
            #[cfg(target_os = "macos")]
            test_direct_accessibility_api,
            #[cfg(target_os = "macos")]
            force_request_accessibility_permission,
            #[cfg(target_os = "macos")]
            request_accessibility_permission_openkey_style,
            #[cfg(target_os = "macos")]
            show_vietnamese_permission_dialog,
            #[cfg(target_os = "macos")]
            open_accessibility_settings_enhanced,
            run_accessibility_diagnostics,
            test_input_monitoring_permissions,
            test_practical_accessibility,
            #[cfg(target_os = "macos")]
            run_comprehensive_accessibility_diagnostics,
            #[cfg(target_os = "macos")]
            test_input_monitoring_permissions_integrated,
            run_vietnamese_engine_tests,
            run_system_diagnostics,
            get_debug_events,
            clear_debug_events,
            test_vietnamese_processing_detailed,
            load_config,
            save_config,
            reset_config_to_defaults,
            backup_config,
            restore_config_from_backup,
            get_all_profiles,
            create_profile,
            update_profile,
            delete_profile,
            get_profile_by_id,
            get_running_applications,
            assign_profile_to_app,
            get_app_icon_path,
            export_profiles,
            import_profiles,
            export_profiles_to_file,
            import_profiles_from_file
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
