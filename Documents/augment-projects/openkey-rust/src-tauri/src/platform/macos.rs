// macOS-specific platform integration
use std::sync::{<PERSON>, <PERSON>tex, OnceLock};
use std::sync::atomic::{AtomicBool, AtomicU64, Ordering};
use std::time::{Duration, Instant};
use std::collections::VecDeque;
use log::{debug, error, info, warn};

use crate::engine::{VietnameseEngine, KeyEvent, ProcessResult};


#[cfg(target_os = "macos")]
use macos_permissions::{PermissionManager, PermissionType, AccessibilityPermission, Permission};

#[cfg(target_os = "macos")]
use core_graphics::{
    event::{CGEvent, CGEventFlags},
    event_source::{CGEventSource},
};



// Direct FFI bindings for CGEventTap functionality
#[cfg(target_os = "macos")]
use std::ffi::c_void;

// CGEvent field constants from CGEventTypes.h
#[cfg(target_os = "macos")]
const K_CG_EVENT_SOURCE_STATE_ID: u32 = 1;
const K_CG_KEYBOARD_EVENT_KEYCODE: u32 = 9;

/// Rate limiter to prevent excessive callback processing
#[derive(Debug)]
struct RateLimiter {
    max_events_per_second: u32,
    window_duration: Duration,
    events: VecDeque<Instant>,
}

impl RateLimiter {
    fn new(max_events_per_second: u32) -> Self {
        Self {
            max_events_per_second,
            window_duration: Duration::from_secs(1),
            events: VecDeque::new(),
        }
    }

    fn should_allow(&mut self) -> bool {
        let now = Instant::now();

        // Remove events outside the time window
        while let Some(&front) = self.events.front() {
            if now.duration_since(front) > self.window_duration {
                self.events.pop_front();
            } else {
                break;
            }
        }

        // Check if we're under the rate limit
        if self.events.len() < self.max_events_per_second as usize {
            self.events.push_back(now);
            true
        } else {
            false
        }
    }

    fn current_rate(&self) -> usize {
        self.events.len()
    }
}

/// Circuit breaker to detect and prevent infinite loops
#[derive(Debug)]
struct CircuitBreaker {
    max_consecutive_events: u32,
    consecutive_count: u32,
    last_event_type: Option<u32>,
    last_reset: Instant,
    reset_interval: Duration,
    is_open: bool,
}

impl CircuitBreaker {
    fn new(max_consecutive_events: u32) -> Self {
        Self {
            max_consecutive_events,
            consecutive_count: 0,
            last_event_type: None,
            last_reset: Instant::now(),
            reset_interval: Duration::from_secs(5),
            is_open: false,
        }
    }

    fn should_allow(&mut self, event_type: u32) -> bool {
        let now = Instant::now();

        // Reset circuit breaker periodically
        if now.duration_since(self.last_reset) > self.reset_interval {
            self.reset();
        }

        // If circuit is open, deny all requests
        if self.is_open {
            return false;
        }

        // Check for consecutive identical events
        if Some(event_type) == self.last_event_type {
            self.consecutive_count += 1;
            if self.consecutive_count > self.max_consecutive_events {
                self.is_open = true;
                warn!("🚨 Circuit breaker OPENED: Too many consecutive events of type {}", event_type);
                return false;
            }
        } else {
            self.consecutive_count = 1;
            self.last_event_type = Some(event_type);
        }

        true
    }

    fn reset(&mut self) {
        self.consecutive_count = 0;
        self.last_event_type = None;
        self.last_reset = Instant::now();
        if self.is_open {
            self.is_open = false;
            info!("🔄 Circuit breaker RESET: Allowing events again");
        }
    }

    fn is_circuit_open(&self) -> bool {
        self.is_open
    }
}

/// Debug mode configuration
#[derive(Debug, Clone)]
struct DebugConfig {
    verbose_logging: bool,
    log_all_events: bool,
    log_rate_limiting: bool,
    log_circuit_breaker: bool,
}

impl DebugConfig {
    fn new() -> Self {
        Self::default()
    }
}

impl Default for DebugConfig {
    fn default() -> Self {
        Self {
            verbose_logging: false,  // Disable verbose logging by default
            log_all_events: false,   // Don't log every single event
            log_rate_limiting: true, // Log rate limiting events
            log_circuit_breaker: true, // Log circuit breaker events
        }
    }
}

/// Global callback protection system
struct CallbackProtection {
    rate_limiter: Mutex<RateLimiter>,
    circuit_breaker: Mutex<CircuitBreaker>,
    debug_config: DebugConfig,
    last_warning: Mutex<Instant>,
    warning_interval: Duration,
}

impl CallbackProtection {
    fn new() -> Self {
        Self {
            rate_limiter: Mutex::new(RateLimiter::new(100)), // Max 100 events per second
            circuit_breaker: Mutex::new(CircuitBreaker::new(50)), // Max 50 consecutive identical events
            debug_config: DebugConfig::default(),
            last_warning: Mutex::new(Instant::now() - Duration::from_secs(10)),
            warning_interval: Duration::from_secs(5),
        }
    }

    fn should_process_event(&self, event_type: u32) -> bool {
        let mut rate_limiter = self.rate_limiter.lock().unwrap();
        let mut circuit_breaker = self.circuit_breaker.lock().unwrap();

        // Check circuit breaker first
        if !circuit_breaker.should_allow(event_type) {
            if self.debug_config.log_circuit_breaker {
                let mut last_warning = self.last_warning.lock().unwrap();
                let now = Instant::now();
                if now.duration_since(*last_warning) > self.warning_interval {
                    warn!("🚫 Event type {} blocked by circuit breaker", event_type);
                    *last_warning = now;
                }
            }
            return false;
        }

        // Check rate limiter
        if !rate_limiter.should_allow() {
            if self.debug_config.log_rate_limiting {
                let mut last_warning = self.last_warning.lock().unwrap();
                let now = Instant::now();
                if now.duration_since(*last_warning) > self.warning_interval {
                    warn!("🚫 Event type {} blocked by rate limiter (current rate: {} events/sec)",
                          event_type, rate_limiter.current_rate());
                    *last_warning = now;
                }
            }
            return false;
        }

        true
    }

    fn log_event(&self, event_type: u32, count: u64) {
        if self.debug_config.log_all_events {
            println!("🚨 CALLBACK #{}: Processing event type {}", count, event_type);
        } else if self.debug_config.verbose_logging && count % 10 == 0 {
            // Log every 10th event when verbose logging is enabled
            println!("📊 Processed {} events (current: type {})", count, event_type);
        }
    }
}

// Injection state tracking to prevent feedback loops
#[derive(Debug)]
struct InjectionState {
    is_injecting: AtomicBool,
    injection_start: AtomicU64,
    cooldown_duration_ms: u64,
}

impl InjectionState {
    fn new() -> Self {
        Self {
            is_injecting: AtomicBool::new(false),
            injection_start: AtomicU64::new(0),
            cooldown_duration_ms: 250, // Increased to 250ms cooldown to prevent feedback loops
        }
    }

    fn start_injection(&self) {
        self.is_injecting.store(true, Ordering::SeqCst);
        let now = Instant::now().elapsed().as_millis() as u64;
        self.injection_start.store(now, Ordering::SeqCst);
        debug!("🔒 Vietnamese injection started - blocking Vietnamese processing");
    }

    fn end_injection(&self) {
        self.is_injecting.store(false, Ordering::SeqCst);
        debug!("🔓 Vietnamese injection ended - Vietnamese processing re-enabled");
    }

    fn is_in_injection(&self) -> bool {
        self.is_injecting.load(Ordering::SeqCst)
    }

    fn is_in_cooldown(&self) -> bool {
        if !self.is_injecting.load(Ordering::SeqCst) {
            let injection_start = self.injection_start.load(Ordering::SeqCst);
            if injection_start > 0 {
                let now = Instant::now().elapsed().as_millis() as u64;
                let elapsed = now.saturating_sub(injection_start);
                return elapsed < self.cooldown_duration_ms;
            }
        }
        false
    }

    fn should_block_vietnamese(&self) -> bool {
        self.is_in_injection() || self.is_in_cooldown()
    }
}

// Vietnamese loop detection to prevent infinite transformations
#[derive(Debug)]
struct VietnameseLoopDetector {
    recent_transformations: Mutex<VecDeque<(String, String, Instant)>>, // (input, output, timestamp)
    max_recent_count: usize,
    loop_threshold: usize,
    time_window: Duration,
}

impl VietnameseLoopDetector {
    fn new() -> Self {
        Self {
            recent_transformations: Mutex::new(VecDeque::new()),
            max_recent_count: 10,
            loop_threshold: 3, // If same transformation happens 3 times in time window, it's a loop
            time_window: Duration::from_millis(500),
        }
    }

    fn record_transformation(&self, input: &str, output: &str) -> bool {
        let mut transformations = self.recent_transformations.lock().unwrap();
        let now = Instant::now();

        // Clean old transformations
        while let Some((_, _, timestamp)) = transformations.front() {
            if now.duration_since(*timestamp) > self.time_window {
                transformations.pop_front();
            } else {
                break;
            }
        }

        // Count identical transformations in recent history
        let identical_count = transformations
            .iter()
            .filter(|(i, o, _)| i == input && o == output)
            .count();

        // Add current transformation
        transformations.push_back((input.to_string(), output.to_string(), now));

        // Keep only recent transformations
        while transformations.len() > self.max_recent_count {
            transformations.pop_front();
        }

        // Check if this is a loop
        let is_loop = identical_count >= self.loop_threshold;
        if is_loop {
            warn!("🔄 Vietnamese loop detected: '{}' -> '{}' (occurred {} times)", input, output, identical_count + 1);
        }

        is_loop
    }

    fn reset(&self) {
        let mut transformations = self.recent_transformations.lock().unwrap();
        transformations.clear();
        debug!("🔄 Vietnamese loop detector reset");
    }
}

// Enhanced protection system with injection state and loop detection
#[derive(Debug)]
struct EnhancedCallbackProtection {
    rate_limiter: Mutex<RateLimiter>,
    circuit_breaker: Mutex<CircuitBreaker>,
    injection_state: InjectionState,
    vietnamese_loop_detector: VietnameseLoopDetector,
    debug_config: DebugConfig,
}

impl EnhancedCallbackProtection {
    fn new() -> Self {
        Self {
            rate_limiter: Mutex::new(RateLimiter::new(100)),
            circuit_breaker: Mutex::new(CircuitBreaker::new(50)),
            injection_state: InjectionState::new(),
            vietnamese_loop_detector: VietnameseLoopDetector::new(),
            debug_config: DebugConfig::new(),
        }
    }

    fn should_process_event(&self, event_type: u32) -> bool {
        // Check rate limiting
        {
            let mut rate_limiter = self.rate_limiter.lock().unwrap();
            if !rate_limiter.should_allow() {
                if self.debug_config.log_rate_limiting {
                    warn!("🚫 Event type {} blocked by rate limiter (current rate: {} events/sec)",
                          event_type, rate_limiter.max_events_per_second);
                }
                return false;
            }
        }

        // Check circuit breaker
        {
            let mut circuit_breaker = self.circuit_breaker.lock().unwrap();
            if !circuit_breaker.should_allow(event_type) {
                if self.debug_config.log_circuit_breaker {
                    warn!("🚫 Event type {} blocked by circuit breaker (consecutive count: {})",
                          event_type, circuit_breaker.consecutive_count);
                }
                return false;
            }
        }

        true
    }

    fn should_process_vietnamese(&self) -> bool {
        if self.injection_state.should_block_vietnamese() {
            debug!("🚫 Vietnamese processing blocked - injection in progress or cooldown");
            return false;
        }
        true
    }

    fn record_vietnamese_transformation(&self, input: &str, output: &str) -> bool {
        !self.vietnamese_loop_detector.record_transformation(input, output)
    }

    fn start_vietnamese_injection(&self) {
        self.injection_state.start_injection();
    }

    fn end_vietnamese_injection(&self) {
        self.injection_state.end_injection();
    }

    fn reset_vietnamese_loop_detector(&self) {
        self.vietnamese_loop_detector.reset();
    }

    fn log_event(&self, event_type: u32, count: u64) {
        if self.debug_config.log_all_events {
            // Log every 50th event when verbose logging is enabled
            if count % 50 == 0 {
                info!("📊 Callback #{}: Event type {} at timestamp {}", count, event_type, Instant::now().elapsed().as_millis());
            }
        } else {
            // Log every 1000th event when verbose logging is disabled
            if count % 1000 == 0 {
                debug!("📊 Processed {} events (current: type {})", count, event_type);
            }
        }
    }
}


// Type definitions to match working minimal test
#[cfg(target_os = "macos")]
pub type CGEventMask = u64;

// Direct FFI declarations to match working minimal test
#[cfg(target_os = "macos")]
#[link(name = "CoreFoundation", kind = "framework")]
#[link(name = "CoreGraphics", kind = "framework")]
extern "C" {
    fn CGEventTapCreate(
        tap: u32,
        place: u32,
        options: u32,
        events_of_interest: CGEventMask,
        callback: extern "C" fn(*mut c_void, u32, *mut c_void, *mut c_void) -> *mut c_void,
        user_info: *mut c_void,
    ) -> *mut c_void;

    fn CGEventTapEnable(tap: *mut c_void, enable: bool);

    fn CFMachPortCreateRunLoopSource(
        allocator: *mut c_void,
        port: *mut c_void,
        order: i32,
    ) -> *mut c_void;

    fn CFRunLoopAddSource(
        rl: *mut c_void,
        source: *mut c_void,
        mode: *mut c_void,
    );

    fn CFRunLoopGetCurrent() -> *mut c_void;

    fn CFRunLoopRun();

    fn CFRunLoopRunInMode(
        mode: *mut c_void,
        seconds: f64,
        return_after_source_handled: bool,
    ) -> i32;

    fn CFRunLoopRemoveSource(
        rl: *mut c_void,
        source: *mut c_void,
        mode: *mut c_void,
    );

    static kCFRunLoopDefaultMode: *mut c_void;

    fn CFRelease(cf: *mut c_void);
    fn CFMachPortIsValid(port: *mut c_void) -> bool;
}

// CGEventTap FFI bindings


// CGEventSource functions for self-event detection
#[cfg(target_os = "macos")]
extern "C" {
    fn CGEventSourceGetSourceStateID(source: *const c_void) -> i64;

    fn CFMachPortInvalidate(port: *mut c_void);

    // Unicode text injection function (like original OpenKey)
    fn CGEventKeyboardSetUnicodeString(event: *mut c_void, string_length: usize, unicode_string: *const u16);

    // Direct CGEvent creation function (like original OpenKey)
    fn CGEventCreateKeyboardEvent(source: *mut c_void, virtual_key: u16, key_down: bool) -> *mut c_void;

    // Event posting
    fn CGEventPost(tap: u32, event: *mut c_void);

    // Event tap posting (ORIGINAL OPENKEY ALGORITHM)
    fn CGEventTapPostEvent(proxy: *mut c_void, event: *mut c_void);

    // Direct CGEvent field access functions (like original OpenKey)
    fn CGEventGetIntegerValueField(event: *const c_void, field: u32) -> i64;
    fn CGEventSetIntegerValueField(event: *mut c_void, field: u32, value: i64);
    fn CGEventGetFlags(event: *const c_void) -> u64;
}

// Global state for CGEventTap callback using safer patterns
use std::sync::atomic::AtomicPtr;

static GLOBAL_ENGINE: OnceLock<Arc<Mutex<VietnameseEngine>>> = OnceLock::new();
static GLOBAL_INTEGRATION: OnceLock<Arc<MacOSIntegration>> = OnceLock::new();

// Use AtomicPtr for thread-safe raw pointer storage
static EVENT_TAP: AtomicPtr<std::ffi::c_void> = AtomicPtr::new(std::ptr::null_mut());
static RUN_LOOP_SOURCE: AtomicPtr<std::ffi::c_void> = AtomicPtr::new(std::ptr::null_mut());




pub struct MacOSIntegration {
    engine: Arc<Mutex<VietnameseEngine>>,
    is_listening: Arc<Mutex<bool>>,
    #[cfg(target_os = "macos")]
    permission_manager: PermissionManager,
    #[cfg(target_os = "macos")]
    accessibility_permission: AccessibilityPermission,
}

impl MacOSIntegration {
    pub fn new(engine: Arc<Mutex<VietnameseEngine>>) -> Self {
        log::info!("🔧 Initializing MacOSIntegration with enhanced permission diagnostics and full CGEventTap support");
        Self {
            engine,
            is_listening: Arc::new(Mutex::new(false)),
            #[cfg(target_os = "macos")]
            permission_manager: PermissionManager::new(),
            #[cfg(target_os = "macos")]
            accessibility_permission: AccessibilityPermission::new(),
        }
    }

    /// Get a reference to the accessibility permission instance
    #[cfg(target_os = "macos")]
    pub fn get_accessibility_permission(&self) -> &AccessibilityPermission {
        &self.accessibility_permission
    }

    pub fn request_accessibility_permission(&self) -> bool {
        log::info!("🔐 MacOSIntegration::request_accessibility_permission() called with enhanced diagnostics");

        // Check if accessibility is already enabled using enhanced checking
        if self.check_accessibility_permission() {
            log::info!("✅ Accessibility permission already granted - no request needed");
            return true;
        }

        log::info!("🔐 Accessibility permission not granted - proceeding with enhanced permission request");

        // Request accessibility permission with enhanced diagnostics
        // Note: Using synchronous fallback to avoid runtime conflicts
        #[cfg(target_os = "macos")]
        {
            log::warn!("⚠️  Using synchronous permission request to avoid runtime conflicts");
            log::info!("🔍 For async permission requests, use request_accessibility_permission_async from async context");

            // Use synchronous permission check as fallback
            match self.accessibility_permission.check_status_sync() {
                Ok(status) => {
                    let is_granted = status.is_granted();
                    log::info!("🔐 Internal permission check completed - Status: {:?}, Granted: {}", status, is_granted);
                    if !is_granted {
                        log::warn!("❌ Accessibility permission not granted");
                        log::info!("🔍 Please go to System Preferences > Security & Privacy > Privacy > Accessibility");
                        log::info!("🔍 Add this application to the list and enable it");
                    }
                    is_granted
                }
                Err(e) => {
                    log::error!("❌ Internal accessibility permission check failed: {}", e);
                    false
                }
            }
        }

        #[cfg(not(target_os = "macos"))]
        {
            log::warn!("❌ Not running on macOS - accessibility permission request not applicable");
            false
        }
    }

    /// Async version of permission request - use this from async contexts like Tauri commands
    pub async fn request_accessibility_permission_async(&self) -> Result<bool, String> {
        log::info!("🔐 MacOSIntegration::request_accessibility_permission_async() called");

        #[cfg(target_os = "macos")]
        {
            match self.accessibility_permission.request_permission().await {
                Ok(result) => {
                    log::info!("🔐 Enhanced permission request completed - Result: {:?}", result);

                    if result.is_success() {
                        log::info!("✅ Permission request successful!");
                        if let Some(message) = &result.message {
                            log::info!("📝 Request message: {}", message);
                        }
                    } else {
                        log::warn!("❌ Permission request failed or user declined");
                        if let Some(message) = &result.message {
                            log::warn!("📝 Request message: {}", message);
                        }
                    }

                    Ok(result.is_success())
                }
                Err(e) => {
                    log::error!("❌ Enhanced accessibility permission request failed: {}", e);
                    Err(e.to_string())
                }
            }
        }

        #[cfg(not(target_os = "macos"))]
        Ok(false)
    }

    /// Async version of permission check - use this from async contexts
    pub async fn check_accessibility_permission_async(&self) -> Result<bool, String> {
        log::info!("🔍 MacOSIntegration::check_accessibility_permission_async() called");

        #[cfg(target_os = "macos")]
        {
            match self.accessibility_permission.check_status().await {
                Ok(status) => {
                    let is_granted = status.is_granted();
                    log::info!("🔐 Enhanced permission check completed - Status: {:?}, Granted: {}", status, is_granted);

                    if !is_granted {
                        log::warn!("❌ Accessibility permission not granted - check debug logs for detailed analysis");
                    } else {
                        log::info!("✅ Accessibility permission confirmed granted with comprehensive validation");
                    }

                    Ok(is_granted)
                }
                Err(e) => {
                    log::error!("❌ Enhanced accessibility permission check failed: {}", e);
                    Err(e.to_string())
                }
            }
        }

        #[cfg(not(target_os = "macos"))]
        Ok(false)
    }

    pub fn check_accessibility_permission(&self) -> bool {
        log::info!("🔍 MacOSIntegration::check_accessibility_permission() called with enhanced diagnostics");

        // Use the enhanced AccessibilityPermission directly for comprehensive diagnostics
        #[cfg(target_os = "macos")]
        {
            log::info!("🍎 Running on macOS, using enhanced AccessibilityPermission with comprehensive diagnostics");
            log::warn!("⚠️  Using synchronous permission check to avoid runtime conflicts");

            // Use synchronous permission check to avoid runtime conflicts
            match self.accessibility_permission.check_status_sync() {
                Ok(status) => {
                    let is_granted = status.is_granted();
                    log::info!("🔐 Internal permission check completed - Status: {:?}, Granted: {}", status, is_granted);

                    if !is_granted {
                        log::warn!("❌ Accessibility permission not granted - check debug logs for detailed analysis");
                        log::warn!("🔍 The enhanced logging above should show exactly why permission check failed");
                    } else {
                        log::info!("✅ Accessibility permission confirmed granted with comprehensive validation");
                    }

                    is_granted
                }
                Err(e) => {
                    log::error!("❌ Internal accessibility permission check failed: {}", e);
                    log::error!("🔍 This indicates a system-level issue with permission checking");
                    false
                }
            }
        }

        #[cfg(not(target_os = "macos"))]
        {
            log::warn!("❌ Not running on macOS - accessibility permissions not applicable");
            false
        }
    }

    /// Force a permission request - this will always show the system dialog if permission is not granted
    pub fn force_request_accessibility_permission(&self) -> bool {
        println!("🔐 MacOSIntegration::force_request_accessibility_permission() called");

        #[cfg(target_os = "macos")]
        {
            log::warn!("⚠️  Force permission request using synchronous fallback to avoid runtime conflicts");

            // Use synchronous permission check as fallback
            match self.accessibility_permission.check_status_sync() {
                Ok(status) => {
                    let is_granted = status.is_granted();
                    println!("🔐 Force permission check result: {}", is_granted);
                    if !is_granted {
                        println!("❌ Permission not granted - user needs to enable manually");
                        println!("🔍 Go to System Preferences > Security & Privacy > Privacy > Accessibility");
                    }
                    is_granted
                }
                Err(e) => {
                    log::error!("Failed to force request accessibility permission: {}", e);
                    false
                }
            }
        }

        #[cfg(not(target_os = "macos"))]
        false
    }

    /// Request permission using the original OpenKey flow with Vietnamese dialog
    pub fn request_accessibility_permission_openkey_style(&self) -> bool {
        println!("🔐 MacOSIntegration::request_accessibility_permission_openkey_style() called");

        #[cfg(target_os = "macos")]
        {
            let accessibility_permission = AccessibilityPermission::new();

            // Use the enhanced OpenKey-style permission request
            match accessibility_permission.request_permission_openkey_style() {
                Ok(result) => {
                    println!("🔐 OpenKey-style permission request result: {:?}", result);

                    if result.is_success() {
                        println!("✅ OpenKey-style permission granted successfully!");
                        if let Some(message) = &result.message {
                            println!("📝 Message: {}", message);
                        }
                    } else {
                        println!("❌ OpenKey-style permission not granted");
                        if let Some(message) = &result.message {
                            println!("📝 Message: {}", message);
                        }
                    }

                    result.is_success()
                }
                Err(e) => {
                    log::error!("Failed to request accessibility permission OpenKey style: {}", e);
                    println!("❌ Error during OpenKey-style permission request: {}", e);
                    false
                }
            }
        }

        #[cfg(not(target_os = "macos"))]
        false
    }

    /// Show Vietnamese permission dialog only (for testing)
    #[cfg(target_os = "macos")]
    pub fn show_vietnamese_permission_dialog(&self) -> bool {
        println!("🇻🇳 MacOSIntegration::show_vietnamese_permission_dialog() called");

        let accessibility_permission = AccessibilityPermission::new();
        match accessibility_permission.show_vietnamese_permission_dialog() {
            Ok(user_granted) => {
                if user_granted {
                    println!("✅ User clicked 'Cấp quyền' (Grant Permission)");
                } else {
                    println!("❌ User clicked 'Không' (Cancel)");
                }
                user_granted
            }
            Err(e) => {
                log::error!("Failed to show Vietnamese permission dialog: {}", e);
                println!("❌ Error showing Vietnamese dialog: {}", e);
                false
            }
        }
    }

    /// Open System Preferences using enhanced method with AppleScript fallback
    #[cfg(target_os = "macos")]
    pub fn open_accessibility_settings_enhanced(&self) -> bool {
        println!("🔧 MacOSIntegration::open_accessibility_settings_enhanced() called");

        let accessibility_permission = AccessibilityPermission::new();
        match accessibility_permission.open_system_preferences() {
            Ok(()) => {
                println!("✅ Successfully opened System Preferences with enhanced method");
                true
            }
            Err(e) => {
                log::error!("Failed to open System Preferences: {}", e);
                println!("❌ Error opening System Preferences: {}", e);
                false
            }
        }
    }

    #[cfg(target_os = "macos")]
    pub fn request_accessibility_permission_with_prompt(&self) -> bool {
        println!("🔐 MacOSIntegration::request_accessibility_permission_with_prompt() called");

        // Use synchronous permission check to avoid runtime conflicts
        log::warn!("⚠️  Using synchronous permission check to avoid runtime conflicts");

        match self.accessibility_permission.check_status_sync() {
            Ok(status) => {
                let is_granted = status.is_granted();
                println!("🔐 Permission request result: {}", is_granted);
                if !is_granted {
                    println!("❌ Permission not granted - user needs to enable manually");
                    println!("🔍 Go to System Preferences > Security & Privacy > Privacy > Accessibility");
                }
                is_granted
            }
            Err(e) => {
                log::error!("Failed to request accessibility permission with prompt: {}", e);
                false
            }
        }
    }

    pub fn start_global_event_capture(&self) -> Result<(), String> {
        let mut is_listening = self.is_listening.lock().unwrap();
        if *is_listening {
            log::info!("🚀 Global event capture already active");
            return Ok(());
        }

        log::info!("🚀 Starting real CGEventTap-based global event capture");

        if !self.check_accessibility_permission() {
            log::error!("❌ Cannot start global event capture - accessibility permission not granted");
            log::error!("🔍 Check the debug logs above for detailed permission analysis");
            return Err("Accessibility permission not granted - check logs for detailed analysis".to_string());
        }

        log::info!("✅ Accessibility permission validated - proceeding with CGEventTap setup");

        #[cfg(target_os = "macos")]
        {
            match self.create_event_tap() {
                Ok(_) => {
                    *is_listening = true;
                    log::info!("✅ CGEventTap-based global keyboard capture started successfully");
                    log::info!("🎯 OpenKey Vietnamese input processing is now active system-wide");
                    log::info!("⌨️  All keyboard events will be processed for Vietnamese input");
                    Ok(())
                }
                Err(e) => {
                    log::error!("❌ Failed to create CGEventTap: {}", e);
                    Err(format!("Failed to create CGEventTap: {}", e))
                }
            }
        }

        #[cfg(not(target_os = "macos"))]
        {
            *is_listening = true;
            log::info!("✅ Vietnamese input processing started (non-macOS mode)");
            Ok(())
        }
    }





    pub fn stop_global_event_capture(&self) {
        let mut is_listening = self.is_listening.lock().unwrap();
        *is_listening = false;

        #[cfg(target_os = "macos")]
        {
            self.cleanup_event_tap();
        }

        log::info!("🛑 Global keyboard event capture stopped");
    }

    pub fn is_listening(&self) -> bool {
        *self.is_listening.lock().unwrap()
    }



    /// Inject Vietnamese characters by simulating backspaces and typing new text (like original OpenKey)
    pub fn inject_vietnamese_text(&self, backspace_count: usize, new_text: &str) -> Result<(), String> {
        log::info!("🇻🇳 [TEXT_INJECTION] Injecting Vietnamese text: backspace {} chars, inject '{}'", backspace_count, new_text);

        // Try the new Unicode CGEvent approach first (like original OpenKey)
        match self.inject_vietnamese_text_unicode(backspace_count, new_text) {
            Ok(()) => {
                log::info!("✅ [TEXT_INJECTION] Vietnamese text injection completed via Unicode CGEvent");
                Ok(())
            }
            Err(e) => {
                log::warn!("⚠️ [TEXT_INJECTION] Unicode CGEvent injection failed: {}, falling back to AppleScript", e);
                // Fallback to AppleScript method
                self.inject_vietnamese_text_applescript(backspace_count, new_text)
            }
        }
    }



    #[cfg(target_os = "macos")]
    fn simulate_character(&self, ch: char) -> Result<(), String> {
        // Use our private event source for character simulation (matching original OpenKey)
        if let Some(key_code) = self.char_to_key_code(ch) {


            unsafe {
                if let Some(ref source) = OUR_EVENT_SOURCE {
                    // Use direct FFI approach to preserve source state ID
                    let private_source_ptr = source.as_ref() as *const _ as *mut c_void;

                    let key_down_ptr = CGEventCreateKeyboardEvent(private_source_ptr, key_code, true);
                    let key_up_ptr = CGEventCreateKeyboardEvent(private_source_ptr, key_code, false);

                    // Post the events using CGEventPost
                    CGEventPost(0, key_down_ptr); // 0 = kCGSessionEventTap
                    CGEventPost(0, key_up_ptr);

                    // Release the events
                    CFRelease(key_down_ptr);
                    CFRelease(key_up_ptr);
                    return Ok(());
                } else {
                    return Err("Private event source not initialized".to_string());
                }
            }
        }

        Err(format!("Failed to simulate character: {}", ch))
    }

    #[cfg(target_os = "macos")]
    fn char_to_key_code(&self, ch: char) -> Option<u16> {
        // Basic ASCII character to macOS key code mapping
        match ch {
            'a'..='z' => Some((ch as u8 - b'a') as u16),
            'A'..='Z' => Some((ch as u8 - b'A') as u16),
            ' ' => Some(49), // Space
            _ => None, // For Vietnamese characters, we'd need a more sophisticated approach
        }
    }

    /// Inject Unicode text using CGEvent with proper Unicode string support (like original OpenKey)
    #[cfg(target_os = "macos")]
    fn inject_unicode_text(&self, text: &str) -> Result<(), String> {
        log::debug!("🔤 [UNICODE_INJECTION] Injecting Unicode text using CGEventKeyboardSetUnicodeString: '{}'", text);

        // Use our private event source for Unicode injection (matching original OpenKey)
        let source = unsafe {
            if let Some(ref source) = OUR_EVENT_SOURCE {
                source.clone()
            } else {
                log::warn!("⚠️ Private event source not initialized, falling back to AppleScript");
                return self.inject_text_via_applescript(text);
            }
        };

        // Convert text to UTF-16 for CGEventKeyboardSetUnicodeString
        let utf16_chars: Vec<u16> = text.encode_utf16().collect();

        if utf16_chars.is_empty() {
            return Ok(());
        }

        // ORIGINAL OPENKEY ALGORITHM: Create keyboard events using our private event source
        // Original: _newEventDown = CGEventCreateKeyboardEvent(myEventSource, 0, true);
        // Original: _newEventUp = CGEventCreateKeyboardEvent(myEventSource, 0, false);
        let (key_down_ptr, key_up_ptr) = unsafe {
            if let Some(our_event_source) = get_our_event_source() {
                log::info!("🔧 [ORIGINAL_ALGORITHM] Creating events with our private event source");
                let key_down = CGEventCreateKeyboardEvent(our_event_source, 0, true);
                let key_up = CGEventCreateKeyboardEvent(our_event_source, 0, false);
                (key_down, key_up)
            } else {
                log::error!("❌ Event source not initialized");
                return Err("Event source not initialized".into());
            }
        };

        if key_down_ptr.is_null() || key_up_ptr.is_null() {
            return Err("Failed to create Unicode keyboard events".to_string());
        }

        // ORIGINAL OPENKEY ALGORITHM: Set Unicode string and post events
        // Original: CGEventKeyboardSetUnicodeString(_newEventDown, 1, &ch);
        // Original: CGEventKeyboardSetUnicodeString(_newEventUp, 1, &ch);
        // Original: CGEventTapPostEvent(_proxy, _newEventDown);
        // Original: CGEventTapPostEvent(_proxy, _newEventUp);
        unsafe {
            CGEventKeyboardSetUnicodeString(key_down_ptr, utf16_chars.len(), utf16_chars.as_ptr());
            CGEventKeyboardSetUnicodeString(key_up_ptr, utf16_chars.len(), utf16_chars.as_ptr());

            // Debug: Check the source state ID of our created events
            let created_source_id = CGEventGetIntegerValueField(key_down_ptr, K_CG_EVENT_SOURCE_STATE_ID);
            log::info!("🔧 [ORIGINAL_ALGORITHM] Injecting text: '{}', Created event source state ID: {}", text, created_source_id);

            // ORIGINAL OPENKEY ALGORITHM: Post events using CGEventTapPostEvent
            // Original: CGEventTapPostEvent(_proxy, _newEventDown);
            // Original: CGEventTapPostEvent(_proxy, _newEventUp);
            CGEventTapPostEvent(CURRENT_PROXY, key_down_ptr);
            CGEventTapPostEvent(CURRENT_PROXY, key_up_ptr);

            // Release the events
            CFRelease(key_down_ptr);
            CFRelease(key_up_ptr);
        }

        log::debug!("✅ [UNICODE_INJECTION] Successfully injected Unicode text: '{}'", text);
        Ok(())
    }

    /// Inject text using AppleScript for better Unicode support
    #[cfg(target_os = "macos")]
    fn inject_text_via_applescript(&self, text: &str) -> Result<(), String> {
        use std::process::Command;

        log::debug!("📝 [APPLESCRIPT_INJECTION] Using AppleScript for text injection: '{}'", text);

        // Escape the text for AppleScript
        let escaped_text = text.replace("\"", "\\\"").replace("\\", "\\\\");

        // Create AppleScript to type the text
        let script = format!(
            r#"tell application "System Events" to keystroke "{}""#,
            escaped_text
        );

        match Command::new("osascript")
            .arg("-e")
            .arg(&script)
            .output()
        {
            Ok(output) if output.status.success() => {
                log::debug!("✅ [APPLESCRIPT_INJECTION] Text injection successful");
                Ok(())
            }
            Ok(output) => {
                let error = String::from_utf8_lossy(&output.stderr);
                log::error!("❌ [APPLESCRIPT_INJECTION] AppleScript failed: {}", error);
                Err(format!("AppleScript execution failed: {}", error))
            }
            Err(e) => {
                log::error!("❌ [APPLESCRIPT_INJECTION] Failed to execute AppleScript: {}", e);
                Err(format!("Failed to execute AppleScript: {}", e))
            }
        }
    }

    /// Open System Preferences to Accessibility settings
    pub fn open_accessibility_settings(&self) -> Result<(), String> {
        #[cfg(target_os = "macos")]
        {
            // Use the new permission manager to open system preferences
            match self.permission_manager.open_system_preferences(PermissionType::Accessibility) {
                Ok(()) => Ok(()),
                Err(e) => Err(format!("Failed to open accessibility settings: {}", e)),
            }
        }

        #[cfg(not(target_os = "macos"))]
        Err("Accessibility settings only available on macOS".to_string())
    }

    /// Run comprehensive accessibility permission diagnostics
    #[cfg(target_os = "macos")]
    pub fn run_comprehensive_accessibility_diagnostics(&self) -> Result<String, String> {
        log::info!("🔍 Running comprehensive accessibility permission diagnostics");

        // Use synchronous diagnostics to avoid runtime conflicts
        log::warn!("⚠️  Using synchronous diagnostics to avoid runtime conflicts");

        let mut results = Vec::new();
        results.push("=== COMPREHENSIVE ACCESSIBILITY DIAGNOSTICS ===".to_string());

        // Test 1: Synchronous permission check
        results.push("\n--- Synchronous Permission Check ---".to_string());
        match self.accessibility_permission.check_status_sync() {
            Ok(status) => {
                let is_granted = status.is_granted();
                results.push(format!("✅ Permission Status: {:?}", status));
                results.push(format!("✅ Permission Granted: {}", is_granted));
                if is_granted {
                    results.push("✅ Accessibility permission is properly configured".to_string());
                } else {
                    results.push("❌ Accessibility permission not granted".to_string());
                    results.push("🔍 User needs to enable in System Preferences".to_string());
                }
            }
            Err(e) => {
                results.push(format!("❌ Permission Check Failed: {}", e));
            }
        }

        // Test 2: Basic system info
        results.push("\n--- System Information ---".to_string());
        results.push("✅ Running on macOS".to_string());
        results.push("✅ Enhanced permission library integrated".to_string());

        Ok(results.join("\n"))
    }

    /// Test Input Monitoring permissions specifically
    #[cfg(target_os = "macos")]
    pub fn test_input_monitoring_permissions(&self) -> Result<String, String> {
        log::info!("🔍 Testing Input Monitoring permissions specifically");

        let mut results = Vec::new();
        results.push("=== INPUT MONITORING DIAGNOSTICS ===".to_string());

        // Test CGEventSource creation
        results.push("\n--- CGEventSource Test ---".to_string());

        use core_graphics::event_source::{CGEventSource, CGEventSourceStateID};

        match CGEventSource::new(CGEventSourceStateID::HIDSystemState) {
            Ok(_) => {
                results.push("✅ CGEventSource creation successful".to_string());
                results.push("📊 Input Monitoring permissions appear to be granted".to_string());
            }
            Err(e) => {
                results.push(format!("❌ CGEventSource creation failed: {:?}", e));
                results.push("📊 Input Monitoring permissions appear to be missing".to_string());
                results.push("🔍 This is separate from Accessibility permissions on macOS 10.15+".to_string());
                results.push("🔍 User needs to grant permission in System Settings > Privacy & Security > Input Monitoring".to_string());
            }
        }

        Ok(results.join("\n"))
    }

    /// Test practical accessibility functionality
    #[cfg(target_os = "macos")]
    pub fn test_practical_accessibility(&self) -> Result<String, String> {
        log::info!("🔍 Testing practical accessibility functionality");

        let mut results = Vec::new();
        results.push("=== PRACTICAL ACCESSIBILITY DIAGNOSTICS ===".to_string());

        // Test 1: Display bounds access
        results.push("\n--- Display Bounds Test ---".to_string());

        use core_graphics::display::CGDisplay;

        match CGDisplay::main().bounds() {
            bounds if bounds.size.width > 0.0 => {
                results.push("✅ Display bounds access successful".to_string());
                results.push(format!("📊 Screen size: {}x{}", bounds.size.width, bounds.size.height));
            }
            bounds => {
                results.push("❌ Display bounds access failed".to_string());
                results.push(format!("📊 Invalid bounds: {}x{}", bounds.size.width, bounds.size.height));
            }
        }

        // Test 2: AppleScript System Events access
        results.push("\n--- AppleScript System Events Test ---".to_string());

        use std::process::Command;

        let script = r#"tell application "System Events" to get name of first application process whose frontmost is true"#;

        match Command::new("osascript").arg("-e").arg(script).output() {
            Ok(output) if output.status.success() => {
                let app_name = String::from_utf8_lossy(&output.stdout).trim().to_string();
                results.push("✅ AppleScript System Events access successful".to_string());
                results.push(format!("📊 Frontmost application: '{}'", app_name));
            }
            Ok(output) => {
                let error = String::from_utf8_lossy(&output.stderr);
                results.push("❌ AppleScript System Events access failed".to_string());
                results.push(format!("📊 Error: {}", error.trim()));
                results.push("🔍 This typically indicates missing accessibility permissions".to_string());
            }
            Err(e) => {
                results.push(format!("❌ Failed to execute AppleScript: {}", e));
            }
        }

        Ok(results.join("\n"))
    }





    /// Enhanced Vietnamese text injection with backspace coordination (Unicode CGEvent version - like original OpenKey)
    pub fn inject_vietnamese_text_unicode(&self, backspace_count: usize, text: &str) -> Result<(), String> {
        log::info!("🇻🇳 [VIETNAMESE_INJECT_UNICODE] Injecting Vietnamese text via Unicode CGEvent: {} backspaces + '{}'",
                  backspace_count, text);

        // First, send backspaces to remove the original characters using CGEvent
        if backspace_count > 0 {
            self.send_backspaces_cgevent(backspace_count)?;
        }

        // Then inject the Vietnamese text using Unicode CGEvent method
        if !text.is_empty() {
            self.inject_unicode_text(text)?;
        }

        log::debug!("✅ Vietnamese text injection completed successfully via Unicode CGEvent");
        Ok(())
    }

    /// Send backspaces using CGEvent (like original OpenKey)
    #[cfg(target_os = "macos")]
    fn send_backspaces_cgevent(&self, count: usize) -> Result<(), String> {


        log::debug!("⌫ [BACKSPACE_CGEVENT] Sending {} backspaces via CGEvent", count);

        // Use our private event source for backspaces (matching original OpenKey)
        let source = unsafe {
            if let Some(ref source) = OUR_EVENT_SOURCE {
                source.clone()
            } else {
                return Err("Private event source not initialized for backspaces".to_string());
            }
        };

        // Key code 51 is backspace on macOS (same as original OpenKey)
        for i in 0..count {
            // Use direct FFI approach like Unicode injection to preserve source state ID
            unsafe {
                let private_source_ptr = source.as_ref() as *const _ as *mut c_void;

                let key_down_ptr = CGEventCreateKeyboardEvent(private_source_ptr, 51, true);
                let key_up_ptr = CGEventCreateKeyboardEvent(private_source_ptr, 51, false);

                // Debug: Check the source state ID of our created backspace events
                let created_source_id = CGEventGetIntegerValueField(key_down_ptr, K_CG_EVENT_SOURCE_STATE_ID);
                log::info!("⌫ [BACKSPACE_DEBUG] Creating backspace event with source state ID: {}", created_source_id);

                // ORIGINAL OPENKEY ALGORITHM: Post events using CGEventTapPostEvent
                // Original: CGEventTapPostEvent(_proxy, eventBackSpaceDown);
                // Original: CGEventTapPostEvent(_proxy, eventBackSpaceUp);
                CGEventTapPostEvent(CURRENT_PROXY, key_down_ptr);
                CGEventTapPostEvent(CURRENT_PROXY, key_up_ptr);

                // Release the events
                CFRelease(key_down_ptr);
                CFRelease(key_up_ptr);
            }

            // Small delay between backspaces for reliability
            if i < count - 1 {
                std::thread::sleep(std::time::Duration::from_millis(1));
            }
        }

        log::debug!("✅ [BACKSPACE_CGEVENT] Successfully sent {} backspaces", count);
        Ok(())
    }

    /// Enhanced Vietnamese text injection with backspace coordination (AppleScript version - fallback)
    pub fn inject_vietnamese_text_applescript(&self, backspace_count: usize, text: &str) -> Result<(), String> {
        log::info!("🇻🇳 [VIETNAMESE_INJECT_AS] Injecting Vietnamese text via AppleScript: {} backspaces + '{}'",
                  backspace_count, text);

        // First, send backspaces to remove the original characters
        if backspace_count > 0 {
            let backspace_script = format!(
                r#"
                tell application "System Events"
                    repeat {} times
                        key code 51
                    end repeat
                end tell
                "#,
                backspace_count
            );

            if let Err(e) = self.execute_applescript_command(&backspace_script) {
                log::error!("❌ Failed to send backspaces: {}", e);
                return Err(format!("Failed to send backspaces: {}", e));
            }

            // Small delay to ensure backspaces are processed
            std::thread::sleep(std::time::Duration::from_millis(1));
        }

        // Then inject the Vietnamese text using the existing method
        if !text.is_empty() {
            self.inject_text_via_applescript(text)?;
        }

        log::debug!("✅ Vietnamese text injection completed successfully");
        Ok(())
    }

    /// Execute AppleScript command
    fn execute_applescript_command(&self, script: &str) -> Result<(), String> {
        use std::process::Command;

        match Command::new("osascript")
            .arg("-e")
            .arg(script)
            .output()
        {
            Ok(output) if output.status.success() => {
                log::debug!("✅ AppleScript executed successfully");
                Ok(())
            }
            Ok(output) => {
                let error = String::from_utf8_lossy(&output.stderr);
                log::error!("❌ AppleScript failed: {}", error);
                Err(format!("AppleScript execution failed: {}", error))
            }
            Err(e) => {
                log::error!("❌ Failed to execute AppleScript: {}", e);
                Err(format!("Failed to execute AppleScript: {}", e))
            }
        }
    }

    /// Inject text using clipboard-based method
    #[cfg(target_os = "macos")]
    fn inject_text_via_clipboard(&self, text: &str) -> Result<(), String> {
        use arboard::Clipboard;
        use std::process::Command;

        log::debug!("📋 [CLIPBOARD_INJECTION] Injecting text via clipboard: '{}'", text);

        // Initialize clipboard
        let mut clipboard = Clipboard::new()
            .map_err(|e| format!("Failed to initialize clipboard: {}", e))?;

        // Save current clipboard content for restoration
        let original_content = clipboard.get_text().ok(); // Don't fail if clipboard is empty

        // Set the text to clipboard
        clipboard.set_text(text)
            .map_err(|e| format!("Failed to set clipboard text: {}", e))?;

        log::debug!("📋 [CLIPBOARD_INJECTION] Text copied to clipboard successfully");

        // Simulate Cmd+V to paste the text
        let paste_script = r#"
            tell application "System Events"
                keystroke "v" using command down
            end tell
        "#;

        let paste_result = Command::new("osascript")
            .arg("-e")
            .arg(paste_script)
            .output()
            .map_err(|e| format!("Failed to execute paste command: {}", e))?;

        if !paste_result.status.success() {
            let error_msg = String::from_utf8_lossy(&paste_result.stderr);
            log::error!("❌ [CLIPBOARD_INJECTION] Paste command failed: {}", error_msg);

            // Try to restore original clipboard content on failure
            if let Some(original) = original_content {
                let _ = clipboard.set_text(original);
            }

            return Err(format!("Clipboard paste failed: {}", error_msg));
        }

        log::debug!("📋 [CLIPBOARD_INJECTION] Paste command executed successfully");

        // Small delay to ensure paste is processed before restoring clipboard
        std::thread::sleep(std::time::Duration::from_millis(50));

        // Restore original clipboard content
        if let Some(original) = original_content {
            if let Err(e) = clipboard.set_text(original) {
                log::warn!("⚠️  [CLIPBOARD_INJECTION] Failed to restore original clipboard content: {}", e);
                // Don't fail the injection for this, just log the warning
            } else {
                log::debug!("📋 [CLIPBOARD_INJECTION] Original clipboard content restored");
            }
        }

        log::debug!("✅ [CLIPBOARD_INJECTION] Text injection completed successfully");
        Ok(())
    }

    /// Create CGEventTap for global keyboard capture
    #[cfg(target_os = "macos")]
    fn create_event_tap(&self) -> Result<(), String> {
        log::info!("🔧 Creating actual CGEventTap for global keyboard capture");

        // Initialize our private event source for self-event detection (matching original OpenKey)
        if let Err(e) = initialize_our_event_source() {
            log::error!("❌ Failed to initialize private event source: {}", e);
            return Err(format!("Failed to initialize private event source: {}", e));
        }

        // Set up global state for callback implementation using safer patterns
        let _ = GLOBAL_ENGINE.set(Arc::clone(&self.engine));
        let _ = GLOBAL_INTEGRATION.set(Arc::new(MacOSIntegration::new(Arc::clone(&self.engine))));



        // Vietnamese processing pipeline is ready - no test needed to avoid polluting state

        // Test the callback infrastructure
        if let Err(e) = vietnamese_event_callback_infrastructure() {
            log::warn!("⚠️  Callback infrastructure test failed: {}", e);
        }

        // Create the actual CGEventTap
        unsafe {
            // Use exact same constants as original OpenKey
            const K_CG_EVENT_KEY_DOWN: u32 = 10;
            const K_CG_EVENT_KEY_UP: u32 = 11;
            const K_CG_EVENT_FLAGS_CHANGED: u32 = 12;
            const K_CG_EVENT_LEFT_MOUSE_DOWN: u32 = 1;
            const K_CG_EVENT_RIGHT_MOUSE_DOWN: u32 = 3;
            const K_CG_EVENT_LEFT_MOUSE_DRAGGED: u32 = 6;
            const K_CG_EVENT_RIGHT_MOUSE_DRAGGED: u32 = 7;

            log::info!("🔧 Using original OpenKey CGEventType constants");

            // Use the EXACT same approach as the working minimal test
            let event_mask: CGEventMask =
                (1u64 << K_CG_EVENT_KEY_DOWN as u64) |
                (1u64 << K_CG_EVENT_KEY_UP as u64) |
                (1u64 << K_CG_EVENT_FLAGS_CHANGED as u64);

            log::info!("🔧 Using WORKING direct FFI approach: 0x{:X}", event_mask);
            log::info!("🔧 Matching the successful minimal test configuration");

            // Use EXACT same parameters as working minimal test
            log::info!("🔧 Creating CGEventTap with Session location (matches working test)");
            log::info!("🔧 Event mask: 0x{:X} (key down: {}, key up: {}, flags: {})", event_mask, K_CG_EVENT_KEY_DOWN, K_CG_EVENT_KEY_UP, K_CG_EVENT_FLAGS_CHANGED);

            // Direct FFI call matching the working minimal test
            let event_tap = CGEventTapCreate(
                1, // kCGSessionEventTap (matches working test)
                0, // kCGHeadInsertEventTap (matches working test)
                1, // kCGEventTapOptionListenOnly (matches working test)
                event_mask,
                enhanced_event_callback,
                std::ptr::null_mut(),
            );

            if event_tap.is_null() {
                log::error!("❌ Failed to create CGEventTap - returned null");
                return Err("CGEventTapCreate returned null - check accessibility permissions".to_string());
            }

            log::info!("✅ CGEventTap created successfully");

            // Create run loop source
            let run_loop_source = CFMachPortCreateRunLoopSource(
                std::ptr::null_mut(), // kCFAllocatorDefault
                event_tap,
                0,
            );

            if run_loop_source.is_null() {
                log::error!("❌ Failed to create run loop source");
                CFRelease(event_tap);
                return Err("Failed to create run loop source".to_string());
            }

            log::info!("✅ Run loop source created successfully");

            // Enable the event tap first
            CGEventTapEnable(event_tap, true);
            log::info!("✅ CGEventTap enabled successfully");

            // Store references for cleanup using AtomicPtr
            EVENT_TAP.store(event_tap as *mut std::ffi::c_void, Ordering::SeqCst);
            RUN_LOOP_SOURCE.store(run_loop_source as *mut std::ffi::c_void, Ordering::SeqCst);

            // CRITICAL FIX: Start background thread and add run loop source to IT, not main thread
            log::info!("🔧 Starting background event processing thread...");
            std::thread::spawn(|| {
                log::info!("🔧 Background thread started - setting up CGEventTap processing");

                // Get the run loop source from the atomic storage
                let run_loop_source_ptr = RUN_LOOP_SOURCE.load(Ordering::SeqCst);
                if !run_loop_source_ptr.is_null() {

                    // Add the run loop source to THIS thread's run loop using direct FFI
                    let background_run_loop = CFRunLoopGetCurrent();
                    CFRunLoopAddSource(background_run_loop, run_loop_source_ptr, kCFRunLoopDefaultMode);
                    log::info!("✅ Run loop source added to background thread's run loop");

                    log::info!("🔧 Background thread now processing CGEventTap events continuously...");
                    let mut iteration = 0;
                    loop {
                        iteration += 1;
                        // Process events for 0.1 seconds, then repeat
                        // Use kCFRunLoopDefaultMode to match the source mode
                        let result = CFRunLoopRunInMode(kCFRunLoopDefaultMode, 0.1, false);

                        // Log every 100 iterations to show the thread is alive and check CGEventTap status
                        if iteration % 100 == 0 {
                            log::debug!("🔄 Background thread alive - iteration {}, result: {}", iteration, result);

                            // Check if CGEventTap is still enabled
                            let event_tap_ptr = EVENT_TAP.load(Ordering::SeqCst);
                            if !event_tap_ptr.is_null() {
                                let is_enabled = CFMachPortIsValid(event_tap_ptr);
                                log::debug!("🔍 CGEventTap status check - Valid: {}", is_enabled);

                                if !is_enabled {
                                    log::warn!("⚠️  CGEventTap has been disabled by system - attempting to re-enable");
                                    CGEventTapEnable(event_tap_ptr, true);
                                }
                            }
                        }

                        // Small sleep to prevent excessive CPU usage
                        std::thread::sleep(std::time::Duration::from_millis(10));
                    }
                } else {
                    log::error!("❌ Background thread: run_loop_source is null");
                }
            });
            log::info!("✅ Background event processing thread started");

            // DEBUGGING: Also try main thread processing to see if that works
            log::info!("🔧 DEBUGGING: Testing main thread CGEventTap processing as well");

            // Get the run loop source from the atomic storage
            let run_loop_source_ptr = RUN_LOOP_SOURCE.load(Ordering::SeqCst);
            if !run_loop_source_ptr.is_null() {
                // Add the run loop source to the MAIN thread's run loop using direct FFI
                let main_run_loop = CFRunLoopGetCurrent(); // Use current instead of main for consistency
                CFRunLoopAddSource(main_run_loop, run_loop_source_ptr, kCFRunLoopDefaultMode);
                log::info!("✅ Run loop source ALSO added to MAIN thread's run loop for testing");

                // Process a few run loop iterations immediately to test
                log::info!("🔧 Processing main run loop iterations for immediate testing...");
                for i in 1..=5 {
                    let result = CFRunLoopRunInMode(kCFRunLoopDefaultMode, 0.01, true); // Process one event, 10ms timeout
                    log::info!("🔧 Main thread iteration {} - CFRunLoop result: {:?}", i, result);

                    let callback_count = CALLBACK_COUNTER.load(Ordering::SeqCst);
                    if callback_count > 0 {
                        log::info!("✅ SUCCESS! CGEventTap callback invoked {} times on main thread", callback_count);
                        break;
                    }
                }

                let final_callback_count = CALLBACK_COUNTER.load(Ordering::SeqCst);
                if final_callback_count > 0 {
                    log::info!("✅ CGEventTap is working on main thread! {} callbacks received", final_callback_count);
                } else {
                    log::warn!("⚠️  CGEventTap callback still not invoked on main thread either");
                }
            } else {
                log::error!("❌ Run loop source is null - cannot test main thread processing");
            }
        }

        log::info!("✅ CGEventTap infrastructure fully initialized and active");
        log::info!("🎯 Global state configured for Vietnamese processing");
        log::info!("⌨️  System-wide Vietnamese input processing is now active");

        Ok(())
    }

    /// Stop and cleanup CGEventTap
    #[cfg(target_os = "macos")]
    fn cleanup_event_tap(&self) {
        log::info!("🛑 Cleaning up CGEventTap resources");

        unsafe {
            // Remove run loop source using AtomicPtr
            let run_loop_source_ptr = RUN_LOOP_SOURCE.swap(std::ptr::null_mut(), Ordering::SeqCst);
            if !run_loop_source_ptr.is_null() {
                let current_run_loop = CFRunLoopGetCurrent();
                CFRunLoopRemoveSource(current_run_loop, run_loop_source_ptr, kCFRunLoopDefaultMode);
                CFRelease(run_loop_source_ptr);
                log::info!("✅ Run loop source removed and released");
            }

            // Disable and cleanup event tap using AtomicPtr
            let event_tap_ptr = EVENT_TAP.swap(std::ptr::null_mut(), Ordering::SeqCst);
            if !event_tap_ptr.is_null() {
                CGEventTapEnable(event_tap_ptr, false);
                CFMachPortInvalidate(event_tap_ptr);
                CFRelease(event_tap_ptr);
                log::info!("✅ CGEventTap disabled and released");
            }

            // Global state is automatically managed by OnceLock and will be cleaned up
            log::info!("✅ Global state cleanup completed");
        }

        log::info!("✅ CGEventTap cleanup completed");
    }



}

/// Implementation of TextInjector trait for MacOSIntegration
impl crate::platform::traits::TextInjector for MacOSIntegration {
    /// Inject text using the best available method (prefer Unicode CGEvent like original OpenKey)
    fn inject_text(&self, text: &str) -> Result<(), crate::platform::traits::PlatformError> {
        log::debug!("💉 [TEXTINJECTOR] Injecting text via TextInjector trait: '{}'", text);

        // Try Unicode CGEvent injection first (like original OpenKey), fallback to AppleScript
        match self.inject_unicode_text(text) {
            Ok(()) => {
                log::debug!("✅ [TEXTINJECTOR] Unicode CGEvent injection successful");
                Ok(())
            }
            Err(e) => {
                log::warn!("⚠️ [TEXTINJECTOR] Unicode CGEvent injection failed: {}, falling back to AppleScript", e);
                self.inject_text_via_applescript(text)
                    .map_err(|e| crate::platform::traits::PlatformError::TextInjectionError(e))
            }
        }
    }

    /// Inject text using specific method
    fn inject_text_with_method(&self, text: &str, method: crate::platform::traits::InjectionMethod) -> Result<(), crate::platform::traits::PlatformError> {
        log::debug!("💉 [TEXTINJECTOR] Injecting text with method {:?}: '{}'", method, text);

        match method {
            crate::platform::traits::InjectionMethod::AppleScript => {
                self.inject_text_via_applescript(text)
                    .map_err(|e| crate::platform::traits::PlatformError::TextInjectionError(e))
            }
            crate::platform::traits::InjectionMethod::Native => {
                self.inject_unicode_text(text)
                    .map_err(|e| crate::platform::traits::PlatformError::TextInjectionError(e))
            }
            crate::platform::traits::InjectionMethod::Accessibility => {
                // Use AppleScript as accessibility method on macOS
                self.inject_text_via_applescript(text)
                    .map_err(|e| crate::platform::traits::PlatformError::TextInjectionError(e))
            }
            crate::platform::traits::InjectionMethod::Clipboard => {
                self.inject_text_via_clipboard(text)
                    .map_err(|e| crate::platform::traits::PlatformError::TextInjectionError(e))
            }
        }
    }

    /// Get available injection methods
    fn get_available_methods(&self) -> Vec<crate::platform::traits::InjectionMethod> {
        vec![
            crate::platform::traits::InjectionMethod::AppleScript,
            crate::platform::traits::InjectionMethod::Native,
            crate::platform::traits::InjectionMethod::Accessibility,
            crate::platform::traits::InjectionMethod::Clipboard,
        ]
    }

    /// Get recommended method for current application
    fn get_recommended_method(&self, _app_info: &crate::platform::traits::ApplicationInfo) -> crate::platform::traits::InjectionMethod {
        // For most applications on macOS, AppleScript provides the best compatibility
        crate::platform::traits::InjectionMethod::AppleScript
    }
}

/// Check if a key event should be processed for Vietnamese input
#[cfg(target_os = "macos")]
#[allow(dead_code)] // Used by CGEventTap callback infrastructure
fn should_process_key_event(event: &KeyEvent) -> bool {
    // Skip modifier-only keys
    if event.is_modifier_only() {
        return false;
    }

    // Skip function keys and special keys
    if event.is_function_key() || event.is_arrow_key() {
        return false;
    }

    // Process printable characters and relevant control keys
    event.is_printable() || event.is_backspace() || event.is_delete()
}

/// Create a text replacement event with backspace simulation
#[cfg(target_os = "macos")]
#[allow(dead_code)] // Used by CGEventTap callback infrastructure
fn create_text_replacement_event(text: &str, backspace_count: usize) -> Result<CGEvent, String> {
    // Use our private event source for text replacement (matching original OpenKey)
    let event_source = unsafe {
        if let Some(ref source) = OUR_EVENT_SOURCE {
            source.clone()
        } else {
            return Err("Private event source not initialized for text replacement".to_string());
        }
    };

    // For now, create a simple text event
    // In a full implementation, we would simulate backspaces followed by text
    let event = CGEvent::new_keyboard_event(event_source, 0, true)
        .map_err(|_| "Failed to create keyboard event")?;

    // Set the Unicode string for the replacement text
    event.set_string(text);

    log::debug!("Created replacement event: {} backspaces, text: '{}'", backspace_count, text);
    Ok(event)
}

/// Create a text append event
#[cfg(target_os = "macos")]
#[allow(dead_code)] // Used by CGEventTap callback infrastructure
fn create_text_append_event(text: &str) -> Result<CGEvent, String> {
    // Use our private event source for text append (matching original OpenKey)
    let event_source = unsafe {
        if let Some(ref source) = OUR_EVENT_SOURCE {
            source.clone()
        } else {
            return Err("Private event source not initialized for text append".to_string());
        }
    };

    let event = CGEvent::new_keyboard_event(event_source, 0, true)
        .map_err(|_| "Failed to create keyboard event")?;

    event.set_string(text);

    log::debug!("Created append event: text: '{}'", text);
    Ok(event)
}

/// Create a delete event
#[cfg(target_os = "macos")]
#[allow(dead_code)] // Used by CGEventTap callback infrastructure
fn create_delete_event(count: usize) -> Result<CGEvent, String> {
    // Use our private event source for delete events (matching original OpenKey)
    let event_source = unsafe {
        if let Some(ref source) = OUR_EVENT_SOURCE {
            source.clone()
        } else {
            return Err("Private event source not initialized for delete events".to_string());
        }
    };

    // Create a backspace event
    let event = CGEvent::new_keyboard_event(event_source, 51, true) // 51 is backspace key code
        .map_err(|_| "Failed to create delete event")?;

    log::debug!("Created delete event: count: {}", count);
    Ok(event)
}

/// Global event source for self-event detection (matching original OpenKey)
#[cfg(target_os = "macos")]
static mut OUR_EVENT_SOURCE: Option<CGEventSource> = None;

/// Global event tap proxy for posting events (matching original OpenKey _proxy)
#[cfg(target_os = "macos")]
static mut CURRENT_PROXY: *mut c_void = std::ptr::null_mut();

/// Initialize our event source with kCGEventSourceStatePrivate (matching original OpenKey)
#[cfg(target_os = "macos")]
fn initialize_our_event_source() -> Result<(), String> {
    use core_graphics::event_source::{CGEventSource, CGEventSourceStateID};

    unsafe {
        if OUR_EVENT_SOURCE.is_none() {
            // Use kCGEventSourceStatePrivate like original OpenKey
            let source = CGEventSource::new(CGEventSourceStateID::Private)
                .map_err(|_| "Failed to create private event source")?;
            OUR_EVENT_SOURCE = Some(source);
            log::info!("🔧 Created private event source for self-event detection");
        }
    }
    Ok(())
}

/// Get our event source for self-event detection (ORIGINAL OPENKEY ALGORITHM)
#[cfg(target_os = "macos")]
fn get_our_event_source() -> Option<*mut c_void> {
    unsafe {
        if let Some(ref source) = OUR_EVENT_SOURCE {
            Some(source.as_ref() as *const _ as *mut c_void)
        } else {
            log::warn!("⚠️ Event source not initialized for self-event detection");
            None
        }
    }
}

/// Get our event source state ID for self-event detection (matching original OpenKey)
#[cfg(target_os = "macos")]
fn get_our_event_source_state_id() -> Option<i64> {
    unsafe {
        if let Some(ref source) = OUR_EVENT_SOURCE {
            // Get the actual state ID from our event source using FFI
            let state_id = CGEventSourceGetSourceStateID(source.as_ref() as *const _ as *const c_void);
            log::debug!("🔧 Using self-event detection with private source state ID: {}", state_id);
            Some(state_id)
        } else {
            log::warn!("🚫 Event source not initialized - self-event detection disabled");
            None
        }
    }
}

// Global callback counter for debugging
static CALLBACK_COUNTER: std::sync::atomic::AtomicU64 = std::sync::atomic::AtomicU64::new(0);

// Global enhanced callback protection system
static ENHANCED_CALLBACK_PROTECTION: OnceLock<EnhancedCallbackProtection> = OnceLock::new();

/// Enhanced CGEventTap callback function for Vietnamese input processing and focus detection
/// Based on original OpenKey callback architecture with rate limiting and circuit breaker protection
#[cfg(target_os = "macos")]
extern "C" fn enhanced_event_callback(
    proxy: *mut c_void,
    event_type: u32,
    event: *mut c_void,
    _refcon: *mut c_void,
) -> *mut c_void {
    // ORIGINAL OPENKEY ALGORITHM: Store the proxy for event posting
    // Original: _proxy = proxy;
    unsafe {
        CURRENT_PROXY = proxy;
    }
    // DEBUG: Log callback entry (reduced verbosity)
    log::debug!("🔥 [CALLBACK_ENTRY] Event type: {}, callback invoked", event_type);

    // CRITICAL: Self-event detection MUST be first, before any protection systems
    // This prevents our own injected events from being processed again
    unsafe {
        let source_state_id = CGEventGetIntegerValueField(event, K_CG_EVENT_SOURCE_STATE_ID);
        // ORIGINAL OPENKEY ALGORITHM: Check if this is our own event and skip processing if so
        // Original: if (CGEventGetIntegerValueField(event, kCGEventSourceStateID) == CGEventSourceGetSourceStateID(myEventSource))
        if let Some(our_event_source) = get_our_event_source() {
            let our_source_state_id = CGEventSourceGetSourceStateID(our_event_source);
            log::info!("🔍 [SELF_EVENT_DEBUG] Event source state ID: {}, Our source state ID: {}", source_state_id, our_source_state_id);
            if source_state_id == our_source_state_id {
                log::info!("🔄 [ORIGINAL_ALGORITHM] ✅ IGNORING our own event (source state ID: {})", source_state_id);
                return event; // Pass through our own events WITHOUT any processing
            } else {
                log::info!("🔍 [ORIGINAL_ALGORITHM] ❌ Processing user event (source state ID: {} != our: {})", source_state_id, our_source_state_id);
            }
        } else {
            log::warn!("⚠️ [SELF_EVENT_DEBUG] No event source available for self-event detection");
        }
    }

    // Increment callback counter
    let count = CALLBACK_COUNTER.fetch_add(1, std::sync::atomic::Ordering::SeqCst) + 1;

    // Apply enhanced rate limiting and circuit breaker protection
    let protection = ENHANCED_CALLBACK_PROTECTION.get_or_init(|| EnhancedCallbackProtection::new());
    if !protection.should_process_event(event_type) {
        // Event blocked by protection system - pass through without processing
        return event;
    }

    // Log event with protection system (respects debug configuration)
    protection.log_event(event_type, count);

    // Add minimal timestamp logging for debugging (only every 50th event to reduce spam)
    if count % 50 == 0 {
        use std::time::{SystemTime, UNIX_EPOCH};
        let timestamp = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
        info!("📊 Callback #{}: Event type {} at timestamp {}", count, event_type, timestamp);
    }

    // Use the same macOS constants as in event mask creation
    const K_CG_EVENT_KEY_DOWN: u32 = 10;
    const K_CG_EVENT_KEY_UP: u32 = 11;
    const K_CG_EVENT_LEFT_MOUSE_DOWN: u32 = 1;
    const K_CG_EVENT_RIGHT_MOUSE_DOWN: u32 = 3;
    const K_CG_EVENT_LEFT_MOUSE_DRAGGED: u32 = 6;
    const K_CG_EVENT_RIGHT_MOUSE_DRAGGED: u32 = 7;

    // Handle different event types like original OpenKey
    match event_type {
        // Keyboard events for Vietnamese input processing
        x if x == K_CG_EVENT_KEY_DOWN => {
            debug!("⌨️  Processing key down event for Vietnamese input");
            let result = handle_keyboard_event(event_type, event);
            debug!("⌨️  Key down processing complete");
            return result;
        }
        // Mouse events that indicate window focus changes (like original OpenKey)
        x if x == K_CG_EVENT_LEFT_MOUSE_DOWN || x == K_CG_EVENT_RIGHT_MOUSE_DOWN ||
             x == K_CG_EVENT_LEFT_MOUSE_DRAGGED || x == K_CG_EVENT_RIGHT_MOUSE_DRAGGED => {
            debug!("🖱️  Focus change event detected");
            handle_potential_focus_change();
            return event; // Pass through mouse events
        }
        _ => {
            return event; // Pass through other events
        }
    }
}

/// Fallback text injection when global integration is not available
#[cfg(target_os = "macos")]
fn inject_text_fallback(backspace_count: usize, text: &str) -> Result<(), String> {
    use std::process::Command;

    println!("🔧 FALLBACK: Injecting {} backspaces + '{}'", backspace_count, text);

    // Send backspaces first
    if backspace_count > 0 {
        let backspace_script = format!(
            r#"tell application "System Events"
                repeat {} times
                    key code 51
                end repeat
            end tell"#,
            backspace_count
        );

        let output = Command::new("osascript")
            .arg("-e")
            .arg(&backspace_script)
            .output()
            .map_err(|e| format!("Failed to execute backspace AppleScript: {}", e))?;

        if !output.status.success() {
            return Err(format!("Backspace AppleScript failed: {}", String::from_utf8_lossy(&output.stderr)));
        }
    }

    // Then inject the Vietnamese text
    if !text.is_empty() {
        let text_script = format!(
            r#"tell application "System Events"
                keystroke "{}"
            end tell"#,
            text.replace("\"", "\\\"")
        );

        let output = Command::new("osascript")
            .arg("-e")
            .arg(&text_script)
            .output()
            .map_err(|e| format!("Failed to execute text AppleScript: {}", e))?;

        if !output.status.success() {
            return Err(format!("Text AppleScript failed: {}", String::from_utf8_lossy(&output.stderr)));
        }
    }

    Ok(())
}

/// Handle keyboard events for Vietnamese input processing
#[cfg(target_os = "macos")]
fn handle_keyboard_event(event_type: u32, event: *mut c_void) -> *mut c_void {
    // Get the global protection system
    let protection = ENHANCED_CALLBACK_PROTECTION.get_or_init(|| EnhancedCallbackProtection::new());

    // Get the global engine safely
    let engine_arc = if let Some(engine) = GLOBAL_ENGINE.get() {
        engine.clone()
    } else {
        warn!("No global engine available in keyboard callback");
        return event;
    };

    // Only process key down events (kCGEventKeyDown = 10)
    // Key up events (kCGEventKeyUp = 11) should be ignored for Vietnamese input
    if event_type != 10 {
        debug!("Ignoring non-key-down event type: {} (expected 10)", event_type);
        return event;
    }

    // Use direct FFI calls to extract key code and flags (matching original OpenKey approach)
    let (key_code, flags_raw) = unsafe {
        let key_code = CGEventGetIntegerValueField(event, K_CG_KEYBOARD_EVENT_KEYCODE) as u16;
        let flags = CGEventGetFlags(event);

        // Debug: Log the actual key code we're receiving
        log::debug!("🔍 Raw key code received: {} (0x{:X})", key_code, key_code);

        (key_code, flags)
    };

    // Convert raw flags to CGEventFlags for compatibility
    let flags = core_graphics::event::CGEventFlags::from_bits_truncate(flags_raw);

    // Create KeyEvent from macOS event data
    let key_event = KeyEvent::from_macos_event(key_code, flags);

    // Check if we can convert to character
    if key_event.to_char().is_none() {
        debug!("Key code {} could not be converted to character", key_code);
        return event; // Skip processing if we can't convert to character
    }

    // Check if Vietnamese processing should be blocked (injection in progress)
    if !protection.should_process_vietnamese() {
        debug!("🚫 Vietnamese processing blocked - injection in progress or cooldown");
        return event; // Pass through without Vietnamese processing
    }

    // Process with Vietnamese engine
    let result = {
        if let Ok(mut engine) = engine_arc.lock() {
            let result = engine.process_key_event(key_event.clone());

            // Log Vietnamese transformations and check for loops
            match &result {
                ProcessResult::ReplaceText { text, backspace_count } => {
                    // Check for Vietnamese transformation loops
                    let input_char = key_event.to_char().unwrap_or('\0').to_string();
                    if !protection.record_vietnamese_transformation(&input_char, text) {
                        warn!("🔄 Vietnamese transformation loop detected - blocking transformation");
                        return event; // Block this transformation to prevent loop
                    }
                    info!("🇻🇳 Vietnamese transformation: '{}' (backspace: {})", text, backspace_count);
                }
                ProcessResult::Replace { new_chars, backspace_count } => {
                    let text: String = new_chars.iter().collect();
                    // Check for Vietnamese transformation loops
                    let input_char = key_event.to_char().unwrap_or('\0').to_string();
                    if !protection.record_vietnamese_transformation(&input_char, &text) {
                        warn!("🔄 Vietnamese transformation loop detected - blocking transformation");
                        return event; // Block this transformation to prevent loop
                    }
                    info!("🇻🇳 Vietnamese transformation: '{}' (backspace: {})", text, backspace_count);
                }
                ProcessResult::PassThrough => {
                    debug!("Character passed through without Vietnamese processing");
                }
                _ => {
                    debug!("Vietnamese engine result: {:?}", result);
                }
            }
            result
        } else {
            warn!("Failed to lock Vietnamese engine");
            return event;
        }
    };

    // Handle the result
    match result {
        ProcessResult::ReplaceText { text, backspace_count } => {
            // Start injection state to prevent feedback loops
            protection.start_vietnamese_injection();

            // Inject Vietnamese text
            if let Some(integration) = GLOBAL_INTEGRATION.get() {
                if let Err(e) = integration.inject_vietnamese_text(backspace_count as usize, &text) {
                    error!("Text injection failed: {}", e);
                    protection.end_vietnamese_injection(); // Re-enable on error
                    return event; // Return original event on injection failure
                } else {
                    debug!("Vietnamese text injection successful: '{}'", text);
                }
            } else {
                // Fallback: Use direct AppleScript injection
                if let Err(e) = inject_text_fallback(backspace_count as usize, &text) {
                    error!("Fallback text injection failed: {}", e);
                    protection.end_vietnamese_injection(); // Re-enable on error
                    return event; // Return original event on injection failure
                } else {
                    debug!("Fallback text injection successful: '{}'", text);
                }
            }

            // Small delay to ensure injected events are processed before re-enabling Vietnamese processing
            std::thread::sleep(std::time::Duration::from_millis(20));

            // CRITICAL: Reset Vietnamese engine state after injection to prevent corruption
            if let Some(engine_arc) = GLOBAL_ENGINE.get() {
                if let Ok(mut engine) = engine_arc.lock() {
                    engine.reset_after_injection();
                    log::debug!("🔄 Vietnamese engine state reset after injection");
                }
            }

            // End injection state
            protection.end_vietnamese_injection();

            // Return null to suppress the original event
            std::ptr::null_mut()
        }
        ProcessResult::Replace { new_chars, backspace_count } => {
            let text: String = new_chars.iter().collect();

            // Start injection state to prevent feedback loops
            protection.start_vietnamese_injection();

            // Inject Vietnamese text
            if let Some(integration) = GLOBAL_INTEGRATION.get() {
                if let Err(e) = integration.inject_vietnamese_text(backspace_count as usize, &text) {
                    error!("Text injection failed: {}", e);
                    protection.end_vietnamese_injection(); // Re-enable on error
                    return event; // Return original event on injection failure
                } else {
                    debug!("Vietnamese text injection successful: '{}'", text);
                }
            } else {
                // Fallback: Use direct AppleScript injection
                if let Err(e) = inject_text_fallback(backspace_count as usize, &text) {
                    error!("Fallback text injection failed: {}", e);
                    protection.end_vietnamese_injection(); // Re-enable on error
                    return event; // Return original event on injection failure
                } else {
                    debug!("Fallback text injection successful: '{}'", text);
                }
            }

            // Small delay to ensure injected events are processed before re-enabling Vietnamese processing
            std::thread::sleep(std::time::Duration::from_millis(20));

            // CRITICAL: Reset Vietnamese engine state after injection to prevent corruption
            if let Some(engine_arc) = GLOBAL_ENGINE.get() {
                if let Ok(mut engine) = engine_arc.lock() {
                    engine.reset_after_injection();
                    log::debug!("🔄 Vietnamese engine state reset after injection");
                }
            }

            // End injection state
            protection.end_vietnamese_injection();

            // Return null to suppress the original event
            std::ptr::null_mut()
        }
        ProcessResult::NoChange | ProcessResult::PassThrough => {
            // Pass through the original event unchanged
            event
        }
        _ => {
            // For other result types, pass through the original event
            debug!("Passing through event for result: {:?}", result);
            event
        }
    }
}

/// Handle potential window focus changes triggered by mouse events
#[cfg(target_os = "macos")]
fn handle_potential_focus_change() {
    // For now, just log that a potential focus change was detected
    // In a full implementation, this would trigger focus detection
    // through a separate focus detection system
    log::debug!("🎯 Potential focus change detected via mouse event");
}

/// Test callback infrastructure (for development and debugging)
#[cfg(target_os = "macos")]
fn vietnamese_event_callback_infrastructure() -> Result<(), String> {
    log::info!("🔧 Testing CGEventTap callback infrastructure");

    // Verify global engine is available without polluting its state
    if GLOBAL_ENGINE.get().is_none() {
        log::warn!("⚠️  No global engine available for callback test");
        return Err("No global engine available".to_string());
    }

    log::info!("✅ CGEventTap callback infrastructure verified and ready");
    Ok(())
}
