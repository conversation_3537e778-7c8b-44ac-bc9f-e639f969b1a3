use std::sync::{<PERSON>, <PERSON>tex};
use std::time::Instant;
use std::collections::VecDeque;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use thiserror::Error;
use tokio::sync::mpsc;

use crate::features::focus_detector::{FocusChangeEvent, FocusDetector, FocusDetectionError};
use crate::features::window_tracker::WindowStateManager;
use crate::features::profile_manager::PersistentProfileManager;
use crate::features::application_profiles::ApplicationProfile;
use crate::features::profile_switching::ProfileSwitchingError;
use crate::engine::VietnameseEngine;

/// Errors that can occur during focus event processing
#[derive(Debug, Error)]
pub enum FocusProcessingError {
    #[error("Processing queue full")]
    QueueFull,
    
    #[error("Processing timeout")]
    Timeout,
    
    #[error("Profile switching failed: {0}")]
    ProfileSwitchFailed(String),
    
    #[error("Window state update failed: {0}")]
    WindowStateUpdateFailed(String),
    
    #[error("Detection error: {0}")]
    DetectionError(#[from] FocusDetectionError),

    #[error("Detection failed: {0}")]
    DetectionFailed(String),

    #[error("Profile switching failed: {0}")]
    ProfileSwitchingFailed(#[from] ProfileSwitchingError),

    #[error("Processing failed: {0}")]
    ProcessingFailed(String),
}

/// Focus event processing configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FocusProcessingConfig {
    /// Maximum number of events in processing queue
    pub max_queue_size: usize,
    
    /// Maximum processing time per event (ms)
    pub max_processing_time_ms: u64,
    
    /// Number of worker threads for processing
    pub worker_threads: usize,
    
    /// Enable profile switching on focus changes
    pub enable_profile_switching: bool,
    
    /// Enable window state updates
    pub enable_window_state_updates: bool,
    
    /// Batch processing size
    pub batch_size: usize,
    
    /// Processing priority levels
    pub high_priority_apps: Vec<String>,
}

impl Default for FocusProcessingConfig {
    fn default() -> Self {
        Self {
            max_queue_size: 1000,
            max_processing_time_ms: 50,
            worker_threads: 2,
            enable_profile_switching: true,
            enable_window_state_updates: true,
            batch_size: 10,
            high_priority_apps: vec![
                "com.apple.dt.Xcode".to_string(),
                "com.microsoft.VSCode".to_string(),
                "com.jetbrains.intellij".to_string(),
            ],
        }
    }
}

/// Focus event processing statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FocusProcessingStats {
    pub total_processed: u64,
    pub successful_switches: u64,
    pub failed_switches: u64,
    pub average_processing_time_ms: f64,
    pub max_processing_time_ms: u64,
    pub queue_size: usize,
    pub queue_overflows: u64,
    pub last_reset: DateTime<Utc>,
}

impl FocusProcessingStats {
    pub fn new() -> Self {
        Self {
            total_processed: 0,
            successful_switches: 0,
            failed_switches: 0,
            average_processing_time_ms: 0.0,
            max_processing_time_ms: 0,
            queue_size: 0,
            queue_overflows: 0,
            last_reset: Utc::now(),
        }
    }
}

/// Priority level for focus events
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord)]
pub enum EventPriority {
    Low = 0,
    Normal = 1,
    High = 2,
    Critical = 3,
}

/// Prioritized focus event for processing
#[derive(Debug, Clone)]
pub struct PrioritizedFocusEvent {
    pub event: FocusChangeEvent,
    pub priority: EventPriority,
    pub queued_at: Instant,
}

/// Focus event processor
pub struct FocusEventProcessor {
    config: FocusProcessingConfig,
    
    // Core components
    focus_detector: Arc<Mutex<FocusDetector>>,
    window_manager: Arc<Mutex<WindowStateManager>>,
    profile_manager: Arc<Mutex<PersistentProfileManager>>,
    
    // Event processing
    event_queue: Arc<Mutex<VecDeque<PrioritizedFocusEvent>>>,
    processing_stats: Arc<Mutex<FocusProcessingStats>>,
    
    // Async processing
    event_sender: Option<mpsc::UnboundedSender<PrioritizedFocusEvent>>,
    event_receiver: Option<mpsc::UnboundedReceiver<PrioritizedFocusEvent>>,
    
    // State tracking
    current_profile: Option<ApplicationProfile>,
    last_processed_event: Option<Instant>,
    processing_times: VecDeque<u64>,
}

impl FocusEventProcessor {
    /// Create a new focus event processor
    pub fn new(
        focus_detector: Arc<Mutex<FocusDetector>>,
        window_manager: Arc<Mutex<WindowStateManager>>,
        profile_manager: Arc<Mutex<PersistentProfileManager>>,
        engine: Arc<Mutex<VietnameseEngine>>,
    ) -> Self {
        Self::with_config(
            focus_detector,
            window_manager,
            profile_manager,
            engine,
            FocusProcessingConfig::default(),
        )
    }
    
    /// Create a new focus event processor with custom configuration
    pub fn with_config(
        focus_detector: Arc<Mutex<FocusDetector>>,
        window_manager: Arc<Mutex<WindowStateManager>>,
        profile_manager: Arc<Mutex<PersistentProfileManager>>,
        _engine: Arc<Mutex<VietnameseEngine>>,
        config: FocusProcessingConfig,
    ) -> Self {
        let (sender, receiver) = mpsc::unbounded_channel();

        Self {
            config,
            focus_detector,
            window_manager,
            profile_manager,
            event_queue: Arc::new(Mutex::new(VecDeque::new())),
            processing_stats: Arc::new(Mutex::new(FocusProcessingStats::new())),
            event_sender: Some(sender),
            event_receiver: Some(receiver),
            current_profile: None,
            last_processed_event: None,
            processing_times: VecDeque::new(),
        }
    }
    
    /// Start the focus event processing system
    pub async fn start(&mut self) -> Result<(), FocusProcessingError> {
        log::info!("🚀 Starting focus event processing system");
        
        // Start focus detection
        if let Ok(mut detector) = self.focus_detector.lock() {
            detector.start()?;
        }
        
        // Start async event processing
        if let Some(mut receiver) = self.event_receiver.take() {
            let processor = self.clone_for_async();
            
            tokio::spawn(async move {
                while let Some(prioritized_event) = receiver.recv().await {
                    if let Err(e) = processor.process_event_async(prioritized_event).await {
                        log::warn!("⚠️ Failed to process focus event: {}", e);
                    }
                }
            });
        }
        
        log::info!("✅ Focus event processing started");
        Ok(())
    }
    
    /// Stop the focus event processing system
    pub async fn stop(&mut self) -> Result<(), FocusProcessingError> {
        log::info!("🛑 Stopping focus event processing system");
        
        // Stop focus detection
        if let Ok(mut detector) = self.focus_detector.lock() {
            detector.stop()?;
        }
        
        // Close event channel
        if let Some(sender) = self.event_sender.take() {
            drop(sender);
        }
        
        log::info!("✅ Focus event processing stopped");
        Ok(())
    }
    
    /// Trigger focus detection (called from CGEventTap callback)
    pub async fn trigger_focus_detection(&mut self) -> Result<(), FocusProcessingError> {
        let start_time = Instant::now();

        // Detect current focus using the focus detector
        let focus_event = {
            if let Ok(mut detector) = self.focus_detector.lock() {
                detector.detect_current_focus()
                    .map_err(|e| FocusProcessingError::DetectionFailed(e.to_string()))?
            } else {
                return Err(FocusProcessingError::DetectionFailed("Failed to lock focus detector".to_string()));
            }
        };

        // Process the focus event if one was detected
        if let Some(event) = focus_event {
            log::debug!("🎯 Focus change detected via CGEventTap trigger: app_changed={}, latency={}ms",
                       event.app_changed, event.detection_latency_ms);

            self.process_focus_change(event)?;
        }

        // Update performance stats
        let trigger_latency = start_time.elapsed().as_millis() as u64;
        if trigger_latency > 50 {
            log::warn!("⚠️ Focus detection trigger took {}ms (target: <50ms)", trigger_latency);
        }

        Ok(())
    }

    /// Process a focus change event
    pub fn process_focus_change(&mut self, event: FocusChangeEvent) -> Result<(), FocusProcessingError> {
        let start_time = Instant::now();

        // Determine event priority
        let priority = self.determine_event_priority(&event);

        // Create prioritized event
        let prioritized_event = PrioritizedFocusEvent {
            event,
            priority,
            queued_at: start_time,
        };

        // Add to queue or send to async processor
        if let Some(sender) = &self.event_sender {
            if sender.send(prioritized_event.clone()).is_err() {
                log::warn!("⚠️ Failed to send event to async processor, using sync processing");
                self.process_event_sync(prioritized_event)?;
            }
        } else {
            self.process_event_sync(prioritized_event)?;
        }

        Ok(())
    }
    
    /// Get processing statistics
    pub fn get_stats(&self) -> FocusProcessingStats {
        if let Ok(stats) = self.processing_stats.lock() {
            stats.clone()
        } else {
            FocusProcessingStats::new()
        }
    }
    
    /// Reset processing statistics
    pub fn reset_stats(&mut self) {
        if let Ok(mut stats) = self.processing_stats.lock() {
            *stats = FocusProcessingStats::new();
        }
        self.processing_times.clear();
    }
    
    /// Determine event priority based on application and context
    fn determine_event_priority(&self, event: &FocusChangeEvent) -> EventPriority {
        if let Some(current_window) = &event.current_window {
            let app_id = &current_window.application.identifier;
            
            // High priority for development tools
            if self.config.high_priority_apps.contains(app_id) {
                return EventPriority::High;
            }
            
            // Critical priority for system applications
            if app_id.contains("system") || app_id.contains("finder") {
                return EventPriority::Critical;
            }
            
            // High priority for app changes
            if event.app_changed {
                return EventPriority::High;
            }
        }
        
        EventPriority::Normal
    }
    
    /// Process event synchronously
    fn process_event_sync(&mut self, prioritized_event: PrioritizedFocusEvent) -> Result<(), FocusProcessingError> {
        let start_time = Instant::now();
        let event = &prioritized_event.event;
        
        // Update window state if enabled
        if self.config.enable_window_state_updates {
            if let Ok(mut detector) = self.focus_detector.lock() {
                detector.process_focus_change(event)
                    .map_err(|e| FocusProcessingError::WindowStateUpdateFailed(e.to_string()))?;
            }
        }
        
        // Switch profile if enabled and needed
        if self.config.enable_profile_switching {
            self.switch_profile_if_needed(event)?;
        }
        
        // Update statistics
        let processing_time = start_time.elapsed().as_millis() as u64;
        self.update_processing_stats(processing_time, true);
        
        log::debug!("✅ Processed focus event in {}ms", processing_time);
        Ok(())
    }
    
    /// Switch profile if needed based on focus change
    fn switch_profile_if_needed(&mut self, event: &FocusChangeEvent) -> Result<(), FocusProcessingError> {
        if let Some(current_window) = &event.current_window {
            let app_identifier = &current_window.application.identifier;
            
            // Simple profile switching logic (placeholder for future implementation)
            log::debug!("🔄 Focus change detected for app: {}", app_identifier);

            // Update statistics
            if let Ok(mut stats) = self.processing_stats.lock() {
                stats.successful_switches += 1;
            }
        }
        
        Ok(())
    }

    /// Update processing statistics
    fn update_processing_stats(&mut self, processing_time_ms: u64, success: bool) {
        if let Ok(mut stats) = self.processing_stats.lock() {
            stats.total_processed += 1;

            if success {
                stats.successful_switches += 1;
            } else {
                stats.failed_switches += 1;
            }

            // Update timing statistics
            self.processing_times.push_back(processing_time_ms);
            if self.processing_times.len() > 100 {
                self.processing_times.pop_front();
            }

            if processing_time_ms > stats.max_processing_time_ms {
                stats.max_processing_time_ms = processing_time_ms;
            }

            // Calculate average processing time
            if !self.processing_times.is_empty() {
                let sum: u64 = self.processing_times.iter().sum();
                stats.average_processing_time_ms = sum as f64 / self.processing_times.len() as f64;
            }

            // Update queue size
            if let Ok(queue) = self.event_queue.lock() {
                stats.queue_size = queue.len();
            }
        }
    }

    /// Clone processor for async processing
    fn clone_for_async(&self) -> AsyncFocusProcessor {
        AsyncFocusProcessor {
            config: self.config.clone(),
            focus_detector: Arc::clone(&self.focus_detector),
            window_manager: Arc::clone(&self.window_manager),
            profile_manager: Arc::clone(&self.profile_manager),
            processing_stats: Arc::clone(&self.processing_stats),
        }
    }
}

/// Async focus processor for background event handling
#[derive(Clone)]
struct AsyncFocusProcessor {
    config: FocusProcessingConfig,
    focus_detector: Arc<Mutex<FocusDetector>>,
    window_manager: Arc<Mutex<WindowStateManager>>,
    profile_manager: Arc<Mutex<PersistentProfileManager>>,
    processing_stats: Arc<Mutex<FocusProcessingStats>>,
}

impl AsyncFocusProcessor {
    /// Process event asynchronously
    async fn process_event_async(&self, prioritized_event: PrioritizedFocusEvent) -> Result<(), FocusProcessingError> {
        let start_time = Instant::now();
        let event = &prioritized_event.event;

        // Check processing timeout
        let queue_time = start_time.duration_since(prioritized_event.queued_at);
        if queue_time.as_millis() > self.config.max_processing_time_ms as u128 {
            log::warn!("⚠️ Event processing timeout: {}ms", queue_time.as_millis());
            return Err(FocusProcessingError::Timeout);
        }

        // Update window state if enabled
        if self.config.enable_window_state_updates {
            if let Ok(mut detector) = self.focus_detector.lock() {
                detector.process_focus_change(event)
                    .map_err(|e| FocusProcessingError::WindowStateUpdateFailed(e.to_string()))?;
            }
        }

        // Switch profile if enabled and needed
        if self.config.enable_profile_switching {
            self.switch_profile_async(event).await?;
        }

        // Update statistics
        let processing_time = start_time.elapsed().as_millis() as u64;
        self.update_stats_async(processing_time, true).await;

        log::debug!("✅ Async processed focus event in {}ms", processing_time);
        Ok(())
    }

    /// Switch profile asynchronously
    async fn switch_profile_async(&self, event: &FocusChangeEvent) -> Result<(), FocusProcessingError> {
        if let Some(current_window) = &event.current_window {
            let app_identifier = &current_window.application.identifier;

            // Simple async profile switching (placeholder)
            log::info!("✅ Async profile switch completed for app: {}", app_identifier);
        }

        Ok(())
    }

    /// Update statistics asynchronously
    async fn update_stats_async(&self, processing_time_ms: u64, success: bool) {
        if let Ok(mut stats) = self.processing_stats.lock() {
            stats.total_processed += 1;

            if success {
                stats.successful_switches += 1;
            } else {
                stats.failed_switches += 1;
            }

            if processing_time_ms > stats.max_processing_time_ms {
                stats.max_processing_time_ms = processing_time_ms;
            }
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::features::application_profiles::ProfileManager;
    use crate::features::app_detector::{ApplicationInfo, WindowInfo, ApplicationType};

    fn create_test_components() -> (
        Arc<Mutex<FocusDetector>>,
        Arc<Mutex<WindowStateManager>>,
        Arc<Mutex<PersistentProfileManager>>,
        Arc<Mutex<VietnameseEngine>>,
    ) {
        let profile_manager = Arc::new(Mutex::new(ProfileManager::new()));
        let window_manager = Arc::new(Mutex::new(WindowStateManager::new(Arc::clone(&profile_manager))));
        let focus_detector = Arc::new(Mutex::new(FocusDetector::new(Arc::clone(&window_manager))));
        let persistent_profile_manager = Arc::new(Mutex::new(
            PersistentProfileManager::new().unwrap()
        ));
        let engine = Arc::new(Mutex::new(VietnameseEngine::new()));

        (focus_detector, window_manager, persistent_profile_manager, engine)
    }

    fn create_test_focus_event() -> FocusChangeEvent {
        let app = ApplicationInfo {
            identifier: "com.test.app".to_string(),
            display_name: "Test App".to_string(),
            process_id: 12345,
            bundle_path: Some("/Applications/TestApp.app".to_string()),
            app_type: ApplicationType::Application,
            executable_path: Some("/Applications/TestApp.app/Contents/MacOS/TestApp".to_string()),
            version: Some("1.0.0".to_string()),
            platform_data: crate::features::app_detector::PlatformData {
                #[cfg(target_os = "macos")]
                macos: None,
                #[cfg(target_os = "windows")]
                windows: None,
            },
            detected_at: chrono::Utc::now(),
        };

        let window = WindowInfo {
            window_id: crate::features::window_tracker::WindowId::new(1, 12345, "com.test.app".to_string()),
            title: "Test Window".to_string(),
            application: app,
            window_class: Some("MainWindow".to_string()),
            bounds: Some((0, 0, 800, 600)),
            window_role: None,
            is_visible: true,
            is_minimized: false,
            is_main: true,
            detected_at: chrono::Utc::now(),
        };

        FocusChangeEvent {
            timestamp: Utc::now(),
            previous_window: None,
            current_window: Some(window),
            app_changed: true,
            detection_latency_ms: 25,
        }
    }

    #[test]
    fn test_focus_processor_creation() {
        let (focus_detector, window_manager, profile_manager, engine) = create_test_components();
        let processor = FocusEventProcessor::new(focus_detector, window_manager, profile_manager, engine);

        assert_eq!(processor.config.max_queue_size, 1000);
        assert_eq!(processor.config.max_processing_time_ms, 50);
        assert!(processor.config.enable_profile_switching);
        assert!(processor.config.enable_window_state_updates);
    }

    #[test]
    fn test_event_priority_determination() {
        let (focus_detector, window_manager, profile_manager, engine) = create_test_components();
        let processor = FocusEventProcessor::new(focus_detector, window_manager, profile_manager, engine);

        let event = create_test_focus_event();
        let priority = processor.determine_event_priority(&event);

        // App change should be high priority
        assert_eq!(priority, EventPriority::High);
    }

    #[test]
    fn test_high_priority_app_detection() {
        let (focus_detector, window_manager, profile_manager, engine) = create_test_components();
        let config = FocusProcessingConfig {
            high_priority_apps: vec!["com.test.app".to_string()],
            ..Default::default()
        };
        let processor = FocusEventProcessor::with_config(
            focus_detector, window_manager, profile_manager, engine, config
        );

        let event = create_test_focus_event();
        let priority = processor.determine_event_priority(&event);

        // Should be high priority due to app being in high priority list
        assert_eq!(priority, EventPriority::High);
    }

    #[test]
    fn test_processing_stats_update() {
        let (focus_detector, window_manager, profile_manager, engine) = create_test_components();
        let mut processor = FocusEventProcessor::new(focus_detector, window_manager, profile_manager, engine);

        processor.update_processing_stats(30, true);
        processor.update_processing_stats(40, false);

        let stats = processor.get_stats();
        assert_eq!(stats.total_processed, 2);
        assert_eq!(stats.successful_switches, 1);
        assert_eq!(stats.failed_switches, 1);
        assert_eq!(stats.max_processing_time_ms, 40);
        assert_eq!(stats.average_processing_time_ms, 35.0);
    }

    #[test]
    fn test_stats_reset() {
        let (focus_detector, window_manager, profile_manager, engine) = create_test_components();
        let mut processor = FocusEventProcessor::new(focus_detector, window_manager, profile_manager, engine);

        processor.update_processing_stats(30, true);
        let stats_before = processor.get_stats();
        assert_eq!(stats_before.total_processed, 1);

        processor.reset_stats();
        let stats_after = processor.get_stats();
        assert_eq!(stats_after.total_processed, 0);
        assert_eq!(stats_after.successful_switches, 0);
        assert_eq!(stats_after.max_processing_time_ms, 0);
    }
}
