use std::path::PathBuf;

// Add the src directory to the module path
fn main() {
    let manifest_dir = std::env::var("CARGO_MANIFEST_DIR").unwrap();
    let src_path = PathBuf::from(manifest_dir).join("src");
    
    // Include the library modules
    mod engine {
        include!("src/engine/mod.rs");
    }
    
    use engine::VietnameseEngine;
    
    println!("🧪 Testing Vietnamese character processing fix...");
    
    // Create a Vietnamese engine
    let mut engine = VietnameseEngine::new();
    
    // Test cases for Telex transformations
    let test_cases = vec![
        ("a", "a"),           // Should stay as 'a'
        ("aw", "ă"),          // Should transform to 'ă'
        ("aa", "â"),          // Should transform to 'â'
        ("ee", "ê"),          // Should transform to 'ê'
        ("oo", "ô"),          // Should transform to 'ô'
        ("uw", "ư"),          // Should transform to 'ư'
        ("dd", "đ"),          // Should transform to 'đ'
        ("as", "á"),          // Should transform to 'á'
        ("af", "à"),          // Should transform to 'à'
        ("ar", "ả"),          // Should transform to 'ả'
        ("ax", "ã"),          // Should transform to 'ã'
        ("aj", "ạ"),          // Should transform to 'ạ'
    ];
    
    println!("\n🔧 Testing process_character method (with our fix):");
    let mut all_passed = true;
    
    for (input, expected) in &test_cases {
        print!("Testing '{}' -> ", input);
        
        // Reset engine state
        engine = VietnameseEngine::new();
        
        // Process each character using the process_character method (which now uses our fix)
        let mut result_chars = Vec::new();
        for ch in input.chars() {
            let result = engine.process_character(ch);
            match result {
                engine::ProcessResult::NoChange => {
                    result_chars.push(ch);
                }
                engine::ProcessResult::Replace { new_chars, .. } => {
                    // Clear previous chars based on backspace_count if needed
                    result_chars.clear();
                    result_chars.extend(new_chars);
                }
                engine::ProcessResult::PassThrough => {
                    result_chars.push(ch);
                }
                engine::ProcessResult::ReplaceText { text, .. } => {
                    result_chars.clear();
                    result_chars.extend(text.chars());
                }
                engine::ProcessResult::Append { text } => {
                    result_chars.extend(text.chars());
                }
                engine::ProcessResult::Delete { .. } => {
                    // Remove characters
                    result_chars.clear();
                }
                _ => {
                    // Handle other variants by adding the character
                    result_chars.push(ch);
                }
            }
        }
        
        let result: String = result_chars.iter().collect();
        let success = result == *expected;
        
        println!("'{}' {}", result, if success { "✅" } else { "❌" });
        
        if !success {
            println!("   Expected: '{}', Got: '{}'", expected, result);
            all_passed = false;
        }
    }
    
    println!("\n🎯 Character processing fix test completed!");
    println!("This confirms that our fix for the key code conversion bug is working correctly.");
    
    if all_passed {
        println!("✅ All tests passed! The character processing fix is working correctly.");
    } else {
        println!("❌ Some tests failed. The character processing fix may need further work.");
    }
}
