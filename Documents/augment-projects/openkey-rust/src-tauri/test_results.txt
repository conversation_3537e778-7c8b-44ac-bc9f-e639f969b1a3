🧪 Enhanced Vietnamese Engine State Reset Test
==============================================

🔍 Debug Test: Correct Telex Tone Transformations
=================================================
Testing correct Telex sequences for common Vietnamese words

Testing: toi → tôi (toi should become tôi (I/me))
  Result: 'toi' → ❌ FAIL
  Direct transformation: 'toi' → 'toi'

Testing: nois → nói (nois should become nói (to speak))
  Result: 'nói' → ✅ PASS

Testing: cos → có (cos should become có (to have))
  Result: 'có' → ✅ PASS

Testing: toof → tồ (toof should become tồ (too + f = tô + f = tồ))
  Result: 'tồ' → ✅ PASS

Testing: noof → nồ (noof should become nồ (noo + f = nô + f = nồ))
  Result: 'nồ' → ✅ PASS

Testing: coof → cồ (coof should become cồ (coo + f = cô + f = cồ))
  Result: 'cồ' → ✅ PASS

Testing: luowi → l<PERSON><PERSON><PERSON> (luowi should become lười (luo + w + i = luơ + i = lười))
  Result: 'luơi' → ❌ FAIL
  Direct transformation: 'luowi' → 'luoưi'

📝 Test 1: Basic Scenario - 'ăn gì chưa vậy ní'
================================================
Expected: Each word should be processed independently
Bug: Engine state corruption causes wrong transformations

Input: 'a' - a
  → NoChange: 'a'

Input: 'w' - ă (aw->ă)
  → Replace (bs:1): 'ă' → Current output: 'ă'
  → Engine state reset after injection

Input: 'n' - n
  → NoChange: 'ăn'

Input: ' ' - space (word boundary)
  → PassThrough (word boundary): 'ăn '

Input: 'g' - g
  → NoChange: 'ăn g'

Input: 'i' - i
  → NoChange: 'ăn gi'

Input: 'f' - ì (gif->gì)
  → Replace (bs:2): 'gì' → Current output: 'ăn gì'
  → Engine state reset after injection

Input: ' ' - space (word boundary)
  → PassThrough (word boundary): 'ăn gì '

Input: 'c' - c
  → NoChange: 'ăn gì c'

Input: 'h' - h
  → NoChange: 'ăn gì ch'

Input: 'w' - w
  → NoChange: 'ăn gì chw'

Input: 'a' - a (chwa->chưa)
  → Replace (bs:3): 'chưa' → Current output: 'ăn gì chưa'
  → Engine state reset after injection

Input: ' ' - space (word boundary)
  → PassThrough (word boundary): 'ăn gì chưa '

Input: 'v' - v
  → NoChange: 'ăn gì chưa v'

Input: 'a' - a
  → NoChange: 'ăn gì chưa va'

Input: 'y' - y
  → NoChange: 'ăn gì chưa vay'

Input: 'a' - a
  → NoChange: 'ăn gì chưa vaya'

Input: 'j' - j (vayaj->vậy)
  → Replace (bs:4): 'vậy' → Current output: 'ăn gì chưa vậy'
  → Engine state reset after injection

Input: ' ' - space (word boundary)
  → PassThrough (word boundary): 'ăn gì chưa vậy '

Input: 'n' - n
  → NoChange: 'ăn gì chưa vậy n'

Input: 'i' - i
  → NoChange: 'ăn gì chưa vậy ni'

Input: 's' - s (nis->ní)
  → Replace (bs:2): 'ní' → Current output: 'ăn gì chưa vậy ní'
  → Engine state reset after injection

🎯 Final Results:
================
Output: 'ăn gì chưa vậy ní'
Words processed: 5
Punctuation boundaries: 0

✅ SUCCESS: Output matches expected result!
   Expected: 'ăn gì chưa vậy ní'
   Actual:   'ăn gì chưa vậy ní'
✅ Test 1 Result: PASSED

📝 Test 2: Punctuation Boundaries
==================================
Testing comma, period, colon, quotation marks as word boundaries

Input: 'X' - X
  → NoChange: 'X'

Input: 'i' - i
  → NoChange: 'Xi'

Input: 'n' - n
  → NoChange: 'Xin'

Input: ' ' - space
  → PassThrough (word boundary): 'Xin '

Input: 'c' - c
  → NoChange: 'Xin c'

Input: 'h' - h
  → NoChange: 'Xin ch'

Input: 'a' - a
  → NoChange: 'Xin cha'

Input: 'f' - f (chaf->chà)
  → Replace (bs:3): 'chà' → Current output: 'Xin chà'
  → Engine state reset after injection

Input: 'o' - o
  → NoChange: 'Xin chào'

Input: 'w' - w (ow->ò -> chào)
  → Replace (bs:4): 'chàơ' → Current output: 'Xin chàơ'
  → Engine state reset after injection

Input: ',' - comma (punctuation boundary)
  → PassThrough (punctuation boundary): 'Xin chàơ,'

Input: ' ' - space
  → PassThrough (word boundary): 'Xin chàơ, '

Input: 't' - t
  → NoChange: 'Xin chàơ, t'

Input: 'o' - o
  → NoChange: 'Xin chàơ, to'

Input: 'o' - o
  → Replace (bs:2): 'tô' → Current output: 'Xin chàơ, tô'
  → Engine state reset after injection

Input: 'f' - f (toof->tôi)
  → Replace (bs:2): 'tồ' → Current output: 'Xin chàơ, tồ'
  → Engine state reset after injection

Input: ' ' - space
  → PassThrough (word boundary): 'Xin chàơ, tồ '

Input: 't' - t
  → NoChange: 'Xin chàơ, tồ t'

Input: 'e' - e
  → NoChange: 'Xin chàơ, tồ te'

Input: 'e' - e
  → Replace (bs:2): 'tê' → Current output: 'Xin chàơ, tồ tê'
  → Engine state reset after injection

Input: 'n' - n (teen->tên)
  → NoChange: 'Xin chàơ, tồ tên'

Input: ' ' - space
  → PassThrough (word boundary): 'Xin chàơ, tồ tên '

Input: 'l' - l
  → NoChange: 'Xin chàơ, tồ tên l'

Input: 'a' - a
  → NoChange: 'Xin chàơ, tồ tên la'

Input: 'f' - f (laf->là)
  → Replace (bs:2): 'là' → Current output: 'Xin chàơ, tồ tên là'
  → Engine state reset after injection

Input: ' ' - space
  → PassThrough (word boundary): 'Xin chàơ, tồ tên là '

Input: 'M' - M
  → NoChange: 'Xin chàơ, tồ tên là M'

Input: 'i' - i
  → NoChange: 'Xin chàơ, tồ tên là Mi'

Input: 'n' - n
  → NoChange: 'Xin chàơ, tồ tên là Min'

Input: 'h' - h
  → NoChange: 'Xin chàơ, tồ tên là Minh'

Input: '.' - period (sentence boundary)
  → PassThrough (punctuation boundary): 'Xin chàơ, tồ tên là Minh.'

Input: ' ' - space
  → PassThrough (word boundary): 'Xin chàơ, tồ tên là Minh. '

Input: 'B' - B
  → NoChange: 'Xin chàơ, tồ tên là Minh. B'

Input: 'a' - a
  → NoChange: 'Xin chàơ, tồ tên là Minh. Ba'

Input: 'n' - n
  → NoChange: 'Xin chàơ, tồ tên là Minh. Ban'

Input: 'j' - j (Banj->Bạn)
  → Replace (bs:3): 'Bạn' → Current output: 'Xin chàơ, tồ tên là Minh. Bạn'
  → Engine state reset after injection

Input: ' ' - space
  → PassThrough (word boundary): 'Xin chàơ, tồ tên là Minh. Bạn '

Input: 'c' - c
  → NoChange: 'Xin chàơ, tồ tên là Minh. Bạn c'

Input: 'o' - o
  → NoChange: 'Xin chàơ, tồ tên là Minh. Bạn co'

Input: 'o' - o
  → Replace (bs:2): 'cô' → Current output: 'Xin chàơ, tồ tên là Minh. Bạn cô'
  → Engine state reset after injection

Input: 'f' - f (coof->có)
  → Replace (bs:2): 'cồ' → Current output: 'Xin chàơ, tồ tên là Minh. Bạn cồ'
  → Engine state reset after injection

Input: ' ' - space
  → PassThrough (word boundary): 'Xin chàơ, tồ tên là Minh. Bạn cồ '

Input: 'k' - k
  → NoChange: 'Xin chàơ, tồ tên là Minh. Bạn cồ k'

Input: 'h' - h
  → NoChange: 'Xin chàơ, tồ tên là Minh. Bạn cồ kh'

Input: 'o' - o
  → NoChange: 'Xin chàơ, tồ tên là Minh. Bạn cồ kho'

Input: 'e' - e
  → NoChange: 'Xin chàơ, tồ tên là Minh. Bạn cồ khoe'

Input: 'r' - r (khoer->khỏe)
  → Replace (bs:4): 'khỏe' → Current output: 'Xin chàơ, tồ tên là Minh. Bạn cồ khỏe'
  → Engine state reset after injection

Input: ' ' - space
  → PassThrough (word boundary): 'Xin chàơ, tồ tên là Minh. Bạn cồ khỏe '

Input: 'k' - k
  → NoChange: 'Xin chàơ, tồ tên là Minh. Bạn cồ khỏe k'

Input: 'h' - h
  → NoChange: 'Xin chàơ, tồ tên là Minh. Bạn cồ khỏe kh'

Input: 'o' - o
  → NoChange: 'Xin chàơ, tồ tên là Minh. Bạn cồ khỏe kho'

Input: 'o' - o
  → Replace (bs:3): 'khô' → Current output: 'Xin chàơ, tồ tên là Minh. Bạn cồ khỏe khô'
  → Engine state reset after injection

Input: 'n' - n
  → NoChange: 'Xin chàơ, tồ tên là Minh. Bạn cồ khỏe khôn'

Input: 'g' - g (khoong->không)
  → NoChange: 'Xin chàơ, tồ tên là Minh. Bạn cồ khỏe không'

Input: '?' - question mark (punctuation boundary)
  → PassThrough (punctuation boundary): 'Xin chàơ, tồ tên là Minh. Bạn cồ khỏe không?'

🎯 Final Results:
================
Output: 'Xin chàơ, tồ tên là Minh. Bạn cồ khỏe không?'
Words processed: 11
Punctuation boundaries: 3

❌ FAILURE: Output does not match expected result!
   Expected: 'Xin chào, tôi tên là Minh. Bạn có khỏe không?'
   Actual:   'Xin chàơ, tồ tên là Minh. Bạn cồ khỏe không?'

🔍 This indicates engine state corruption or boundary handling issues
✅ Test 2 Result: FAILED

📝 Test 3: Line Breaks and Formatting
======================================
Testing newlines and multi-line text with Vietnamese transformations

Input: 'H' - H
  → NoChange: 'H'

Input: 'o' - o
  → NoChange: 'Ho'

Input: 'o' - o
  → Replace (bs:2): 'Hô' → Current output: 'Hô'
  → Engine state reset after injection

Input: 'm' - m (Hoom->Hôm)
  → NoChange: 'Hôm'

Input: ' ' - space
  → PassThrough (word boundary): 'Hôm '

Input: 'n' - n
  → NoChange: 'Hôm n'

Input: 'a' - a
  → NoChange: 'Hôm na'

Input: 'y' - y
  → NoChange: 'Hôm nay'

Input: ' ' - space
  → PassThrough (word boundary): 'Hôm nay '

Input: 't' - t
  → NoChange: 'Hôm nay t'

Input: 'r' - r
  → NoChange: 'Hôm nay tr'

Input: 'o' - o
  → NoChange: 'Hôm nay tro'

Input: 'w' - w
  → Replace (bs:3): 'trơ' → Current output: 'Hôm nay trơ'
  → Engine state reset after injection

Input: 'f' - f (trowf->trời)
  → Replace (bs:3): 'trờ' → Current output: 'Hôm nay trờ'
  → Engine state reset after injection

Input: ' ' - space
  → PassThrough (word boundary): 'Hôm nay trờ '

Input: 'd' - d
  → NoChange: 'Hôm nay trờ d'

Input: 'e' - e
  → NoChange: 'Hôm nay trờ de'

Input: 'e' - e
  → Replace (bs:2): 'dê' → Current output: 'Hôm nay trờ dê'
  → Engine state reset after injection

Input: 'p' - p (deep->đẹp)
  → NoChange: 'Hôm nay trờ dêp'

Input: '.' - period
  → PassThrough (punctuation boundary): 'Hôm nay trờ dêp.'

Input: '\n' - newline (line boundary)
  → PassThrough (word boundary): 'Hôm nay trờ dêp.\n'

Input: 'T' - T
  → NoChange: 'Hôm nay trờ dêp.\nT'

Input: 'o' - o
  → NoChange: 'Hôm nay trờ dêp.\nTo'

Input: 'o' - o
  → Replace (bs:2): 'Tô' → Current output: 'Hôm nay trờ dêp.\nTô'
  → Engine state reset after injection

Input: 'f' - f (Toof->Tôi)
  → Replace (bs:2): 'Tồ' → Current output: 'Hôm nay trờ dêp.\nTồ'
  → Engine state reset after injection

Input: ' ' - space
  → PassThrough (word boundary): 'Hôm nay trờ dêp.\nTồ '

Input: 'm' - m
  → NoChange: 'Hôm nay trờ dêp.\nTồ m'

Input: 'u' - u
  → NoChange: 'Hôm nay trờ dêp.\nTồ mu'

Input: 'o' - o
  → NoChange: 'Hôm nay trờ dêp.\nTồ muo'

Input: 'n' - n (muon->muốn)
  → NoChange: 'Hôm nay trờ dêp.\nTồ muon'

Input: ' ' - space
  → PassThrough (word boundary): 'Hôm nay trờ dêp.\nTồ muon '

Input: 'd' - d
  → NoChange: 'Hôm nay trờ dêp.\nTồ muon d'

Input: 'i' - i (di->đi)
  → NoChange: 'Hôm nay trờ dêp.\nTồ muon di'

Input: ' ' - space
  → PassThrough (word boundary): 'Hôm nay trờ dêp.\nTồ muon di '

Input: 'c' - c
  → NoChange: 'Hôm nay trờ dêp.\nTồ muon di c'

Input: 'h' - h
  → NoChange: 'Hôm nay trờ dêp.\nTồ muon di ch'

Input: 'o' - o
  → NoChange: 'Hôm nay trờ dêp.\nTồ muon di cho'

Input: 'w' - w
  → Replace (bs:3): 'chơ' → Current output: 'Hôm nay trờ dêp.\nTồ muon di chơ'
  → Engine state reset after injection

Input: 'f' - f (chowf->chơi)
  → Replace (bs:3): 'chờ' → Current output: 'Hôm nay trờ dêp.\nTồ muon di chờ'
  → Engine state reset after injection

Input: '.' - period
  → PassThrough (punctuation boundary): 'Hôm nay trờ dêp.\nTồ muon di chờ.'

Input: '\n' - newline (line boundary)
  → PassThrough (word boundary): 'Hôm nay trờ dêp.\nTồ muon di chờ.\n'

Input: 'B' - B
  → NoChange: 'Hôm nay trờ dêp.\nTồ muon di chờ.\nB'

Input: 'a' - a
  → NoChange: 'Hôm nay trờ dêp.\nTồ muon di chờ.\nBa'

Input: 'n' - n
  → NoChange: 'Hôm nay trờ dêp.\nTồ muon di chờ.\nBan'

Input: 'j' - j (Banj->Bạn)
  → Replace (bs:3): 'Bạn' → Current output: 'Hôm nay trờ dêp.\nTồ muon di chờ.\nBạn'
  → Engine state reset after injection

Input: ' ' - space
  → PassThrough (word boundary): 'Hôm nay trờ dêp.\nTồ muon di chờ.\nBạn '

Input: 'c' - c
  → NoChange: 'Hôm nay trờ dêp.\nTồ muon di chờ.\nBạn c'

Input: 'o' - o
  → NoChange: 'Hôm nay trờ dêp.\nTồ muon di chờ.\nBạn co'

Input: 'o' - o
  → Replace (bs:2): 'cô' → Current output: 'Hôm nay trờ dêp.\nTồ muon di chờ.\nBạn cô'
  → Engine state reset after injection

Input: 'f' - f (coof->có)
  → Replace (bs:2): 'cồ' → Current output: 'Hôm nay trờ dêp.\nTồ muon di chờ.\nBạn cồ'
  → Engine state reset after injection

Input: ' ' - space
  → PassThrough (word boundary): 'Hôm nay trờ dêp.\nTồ muon di chờ.\nBạn cồ '

Input: 'm' - m
  → NoChange: 'Hôm nay trờ dêp.\nTồ muon di chờ.\nBạn cồ m'

Input: 'u' - u
  → NoChange: 'Hôm nay trờ dêp.\nTồ muon di chờ.\nBạn cồ mu'

Input: 'o' - o
  → NoChange: 'Hôm nay trờ dêp.\nTồ muon di chờ.\nBạn cồ muo'

Input: 'n' - n (muon->muốn)
  → NoChange: 'Hôm nay trờ dêp.\nTồ muon di chờ.\nBạn cồ muon'

Input: ' ' - space
  → PassThrough (word boundary): 'Hôm nay trờ dêp.\nTồ muon di chờ.\nBạn cồ muon '

Input: 'd' - d
  → NoChange: 'Hôm nay trờ dêp.\nTồ muon di chờ.\nBạn cồ muon d'

Input: 'i' - i (di->đi)
  → NoChange: 'Hôm nay trờ dêp.\nTồ muon di chờ.\nBạn cồ muon di'

Input: ' ' - space
  → PassThrough (word boundary): 'Hôm nay trờ dêp.\nTồ muon di chờ.\nBạn cồ muon di '

Input: 'k' - k
  → NoChange: 'Hôm nay trờ dêp.\nTồ muon di chờ.\nBạn cồ muon di k'

Input: 'h' - h
  → NoChange: 'Hôm nay trờ dêp.\nTồ muon di chờ.\nBạn cồ muon di kh'

Input: 'o' - o
  → NoChange: 'Hôm nay trờ dêp.\nTồ muon di chờ.\nBạn cồ muon di kho'

Input: 'o' - o
  → Replace (bs:3): 'khô' → Current output: 'Hôm nay trờ dêp.\nTồ muon di chờ.\nBạn cồ muon di khô'
  → Engine state reset after injection

Input: 'n' - n
  → NoChange: 'Hôm nay trờ dêp.\nTồ muon di chờ.\nBạn cồ muon di khôn'

Input: 'g' - g (khoong->không)
  → NoChange: 'Hôm nay trờ dêp.\nTồ muon di chờ.\nBạn cồ muon di không'

Input: '?' - question mark
  → PassThrough (punctuation boundary): 'Hôm nay trờ dêp.\nTồ muon di chờ.\nBạn cồ muon di không?'

🎯 Final Results:
================
Output: 'Hôm nay trờ dêp.\nTồ muon di chờ.\nBạn cồ muon di không?'
Words processed: 12
Punctuation boundaries: 3

❌ FAILURE: Output does not match expected result!
   Expected: 'Hôm nay trời đẹp.\nTôi muốn đi chơi.\nBạn có muốn đi không?'
   Actual:   'Hôm nay trờ dêp.\nTồ muon di chờ.\nBạn cồ muon di không?'

🔍 This indicates engine state corruption or boundary handling issues
✅ Test 3 Result: FAILED

📝 Test 4: Realistic Vietnamese Text
=====================================
Testing a longer, realistic Vietnamese paragraph with quotes and complex punctuation

Input: 'M' - M
  → NoChange: 'M'

Input: 'e' - e
  → NoChange: 'Me'

Input: 'j' - j (Mej->Mẹ)
  → Replace (bs:2): 'Mẹ' → Current output: 'Mẹ'
  → Engine state reset after injection

Input: ' ' - space
  → PassThrough (word boundary): 'Mẹ '

Input: 'n' - n
  → NoChange: 'Mẹ n'

Input: 'o' - o
  → NoChange: 'Mẹ no'

Input: 'o' - o
  → Replace (bs:2): 'nô' → Current output: 'Mẹ nô'
  → Engine state reset after injection

Input: 'f' - f (noof->nói)
  → Replace (bs:2): 'nồ' → Current output: 'Mẹ nồ'
  → Engine state reset after injection

Input: ':' - colon (punctuation boundary)
  → PassThrough (punctuation boundary): 'Mẹ nồ:'

Input: ' ' - space
  → PassThrough (word boundary): 'Mẹ nồ: '

Input: '"' - opening quote (punctuation boundary)
  → PassThrough (punctuation boundary): 'Mẹ nồ: "'

Input: 'C' - C
  → NoChange: 'Mẹ nồ: "C'

Input: 'o' - o
  → NoChange: 'Mẹ nồ: "Co'

Input: 'n' - n
  → NoChange: 'Mẹ nồ: "Con'

Input: ' ' - space
  → PassThrough (word boundary): 'Mẹ nồ: "Con '

Input: 'p' - p
  → NoChange: 'Mẹ nồ: "Con p'

Input: 'h' - h
  → NoChange: 'Mẹ nồ: "Con ph'

Input: 'a' - a
  → NoChange: 'Mẹ nồ: "Con pha'

Input: 'i' - i
  → NoChange: 'Mẹ nồ: "Con phai'

Input: 'f' - f (phaif->phải)
  → Replace (bs:4): 'phài' → Current output: 'Mẹ nồ: "Con phài'
  → Engine state reset after injection

Input: ' ' - space
  → PassThrough (word boundary): 'Mẹ nồ: "Con phài '

Input: 'h' - h
  → NoChange: 'Mẹ nồ: "Con phài h'

Input: 'o' - o
  → NoChange: 'Mẹ nồ: "Con phài ho'

Input: 'c' - c
  → NoChange: 'Mẹ nồ: "Con phài hoc'

Input: 'j' - j (hocj->học)
  → Replace (bs:3): 'học' → Current output: 'Mẹ nồ: "Con phài học'
  → Engine state reset after injection

Input: ' ' - space
  → PassThrough (word boundary): 'Mẹ nồ: "Con phài học '

Input: 'h' - h
  → NoChange: 'Mẹ nồ: "Con phài học h'

Input: 'a' - a
  → NoChange: 'Mẹ nồ: "Con phài học ha'

Input: 'n' - n
  → NoChange: 'Mẹ nồ: "Con phài học han'

Input: 'h' - h
  → NoChange: 'Mẹ nồ: "Con phài học hanh'

Input: 'f' - f (hanhf->hành)
  → Replace (bs:4): 'hành' → Current output: 'Mẹ nồ: "Con phài học hành'
  → Engine state reset after injection

Input: ' ' - space
  → PassThrough (word boundary): 'Mẹ nồ: "Con phài học hành '

Input: 'c' - c
  → NoChange: 'Mẹ nồ: "Con phài học hành c'

Input: 'h' - h
  → NoChange: 'Mẹ nồ: "Con phài học hành ch'

Input: 'a' - a
  → NoChange: 'Mẹ nồ: "Con phài học hành cha'

Input: 'm' - m (cham->chăm)
  → NoChange: 'Mẹ nồ: "Con phài học hành cham'

Input: ' ' - space
  → PassThrough (word boundary): 'Mẹ nồ: "Con phài học hành cham '

Input: 'c' - c
  → NoChange: 'Mẹ nồ: "Con phài học hành cham c'

Input: 'h' - h
  → NoChange: 'Mẹ nồ: "Con phài học hành cham ch'

Input: 'i' - i
  → NoChange: 'Mẹ nồ: "Con phài học hành cham chi'

Input: 'r' - r (chir->chỉ)
  → Replace (bs:3): 'chỉ' → Current output: 'Mẹ nồ: "Con phài học hành cham chỉ'
  → Engine state reset after injection

Input: ',' - comma (punctuation boundary)
  → PassThrough (punctuation boundary): 'Mẹ nồ: "Con phài học hành cham chỉ,'

Input: ' ' - space
  → PassThrough (word boundary): 'Mẹ nồ: "Con phài học hành cham chỉ, '

Input: 'd' - d
  → NoChange: 'Mẹ nồ: "Con phài học hành cham chỉ, d'

Input: 'u' - u
  → NoChange: 'Mẹ nồ: "Con phài học hành cham chỉ, du'

Input: 'w' - w
  → Replace (bs:2): 'dư' → Current output: 'Mẹ nồ: "Con phài học hành cham chỉ, dư'
  → Engine state reset after injection

Input: 'n' - n
  → NoChange: 'Mẹ nồ: "Con phài học hành cham chỉ, dưn'

Input: 'g' - g
  → NoChange: 'Mẹ nồ: "Con phài học hành cham chỉ, dưng'

Input: 'f' - f (duwngf->đừng)
  → Replace (bs:4): 'dừng' → Current output: 'Mẹ nồ: "Con phài học hành cham chỉ, dừng'
  → Engine state reset after injection

Input: ' ' - space
  → PassThrough (word boundary): 'Mẹ nồ: "Con phài học hành cham chỉ, dừng '

Input: 'l' - l
  → NoChange: 'Mẹ nồ: "Con phài học hành cham chỉ, dừng l'

Input: 'u' - u
  → NoChange: 'Mẹ nồ: "Con phài học hành cham chỉ, dừng lu'

Input: 'o' - o
  → NoChange: 'Mẹ nồ: "Con phài học hành cham chỉ, dừng luo'

Input: 'w' - w
  → Replace (bs:3): 'luơ' → Current output: 'Mẹ nồ: "Con phài học hành cham chỉ, dừng luơ'
  → Engine state reset after injection

Input: 'f' - f (luowf->lười)
  → Replace (bs:3): 'luờ' → Current output: 'Mẹ nồ: "Con phài học hành cham chỉ, dừng luờ'
  → Engine state reset after injection

Input: ' ' - space
  → PassThrough (word boundary): 'Mẹ nồ: "Con phài học hành cham chỉ, dừng luờ '

Input: 'b' - b
  → NoChange: 'Mẹ nồ: "Con phài học hành cham chỉ, dừng luờ b'

Input: 'i' - i
  → NoChange: 'Mẹ nồ: "Con phài học hành cham chỉ, dừng luờ bi'

Input: 'e' - e
  → NoChange: 'Mẹ nồ: "Con phài học hành cham chỉ, dừng luờ bie'

Input: 'e' - e
  → Replace (bs:3): 'biê' → Current output: 'Mẹ nồ: "Con phài học hành cham chỉ, dừng luờ biê'
  → Engine state reset after injection

Input: 'n' - n
  → NoChange: 'Mẹ nồ: "Con phài học hành cham chỉ, dừng luờ biên'

Input: 'g' - g
  → NoChange: 'Mẹ nồ: "Con phài học hành cham chỉ, dừng luờ biêng'

Input: 's' - s (bieengs->biếng)
  → Replace (bs:5): 'biếng' → Current output: 'Mẹ nồ: "Con phài học hành cham chỉ, dừng luờ biếng'
  → Engine state reset after injection

Input: '!' - exclamation (punctuation boundary)
  → PassThrough (punctuation boundary): 'Mẹ nồ: "Con phài học hành cham chỉ, dừng luờ biếng!'

Input: '"' - closing quote (punctuation boundary)
  → PassThrough (punctuation boundary): 'Mẹ nồ: "Con phài học hành cham chỉ, dừng luờ biếng!"'

Input: ' ' - space
  → PassThrough (word boundary): 'Mẹ nồ: "Con phài học hành cham chỉ, dừng luờ biếng!" '

Input: 'T' - T
  → NoChange: 'Mẹ nồ: "Con phài học hành cham chỉ, dừng luờ biếng!" T'

Input: 'o' - o
  → NoChange: 'Mẹ nồ: "Con phài học hành cham chỉ, dừng luờ biếng!" To'

Input: 'o' - o
  → Replace (bs:2): 'Tô' → Current output: 'Mẹ nồ: "Con phài học hành cham chỉ, dừng luờ biếng!" Tô'
  → Engine state reset after injection

Input: 'f' - f (Toof->Tôi)
  → Replace (bs:2): 'Tồ' → Current output: 'Mẹ nồ: "Con phài học hành cham chỉ, dừng luờ biếng!" Tồ'
  → Engine state reset after injection

Input: ' ' - space
  → PassThrough (word boundary): 'Mẹ nồ: "Con phài học hành cham chỉ, dừng luờ biếng!" Tồ '

Input: 't' - t
  → NoChange: 'Mẹ nồ: "Con phài học hành cham chỉ, dừng luờ biếng!" Tồ t'

Input: 'r' - r
  → NoChange: 'Mẹ nồ: "Con phài học hành cham chỉ, dừng luờ biếng!" Tồ tr'

Input: 'a' - a
  → NoChange: 'Mẹ nồ: "Con phài học hành cham chỉ, dừng luờ biếng!" Tồ tra'

Input: 'r' - r (trar->trả)
  → Replace (bs:3): 'trả' → Current output: 'Mẹ nồ: "Con phài học hành cham chỉ, dừng luờ biếng!" Tồ trả'
  → Engine state reset after injection

Input: ' ' - space
  → PassThrough (word boundary): 'Mẹ nồ: "Con phài học hành cham chỉ, dừng luờ biếng!" Tồ trả '

Input: 'l' - l
  → NoChange: 'Mẹ nồ: "Con phài học hành cham chỉ, dừng luờ biếng!" Tồ trả l'

Input: 'o' - o
  → NoChange: 'Mẹ nồ: "Con phài học hành cham chỉ, dừng luờ biếng!" Tồ trả lo'

Input: 'w' - w
  → Replace (bs:2): 'lơ' → Current output: 'Mẹ nồ: "Con phài học hành cham chỉ, dừng luờ biếng!" Tồ trả lơ'
  → Engine state reset after injection

Input: 'f' - f (lowf->lời)
  → Replace (bs:2): 'lờ' → Current output: 'Mẹ nồ: "Con phài học hành cham chỉ, dừng luờ biếng!" Tồ trả lờ'
  → Engine state reset after injection

Input: ':' - colon (punctuation boundary)
  → PassThrough (punctuation boundary): 'Mẹ nồ: "Con phài học hành cham chỉ, dừng luờ biếng!" Tồ trả lờ:'

Input: ' ' - space
  → PassThrough (word boundary): 'Mẹ nồ: "Con phài học hành cham chỉ, dừng luờ biếng!" Tồ trả lờ: '

Input: '"' - opening quote (punctuation boundary)
  → PassThrough (punctuation boundary): 'Mẹ nồ: "Con phài học hành cham chỉ, dừng luờ biếng!" Tồ trả lờ: "'

Input: 'V' - V
  → NoChange: 'Mẹ nồ: "Con phài học hành cham chỉ, dừng luờ biếng!" Tồ trả lờ: "V'

Input: 'a' - a
  → NoChange: 'Mẹ nồ: "Con phài học hành cham chỉ, dừng luờ biếng!" Tồ trả lờ: "Va'

Input: 'a' - a
  → Replace (bs:2): 'Vâ' → Current output: 'Mẹ nồ: "Con phài học hành cham chỉ, dừng luờ biếng!" Tồ trả lờ: "Vâ'
  → Engine state reset after injection

Input: 'n' - n
  → NoChange: 'Mẹ nồ: "Con phài học hành cham chỉ, dừng luờ biếng!" Tồ trả lờ: "Vân'

Input: 'g' - g (Vaang->Vâng)
  → NoChange: 'Mẹ nồ: "Con phài học hành cham chỉ, dừng luờ biếng!" Tồ trả lờ: "Vâng'

Input: ',' - comma (punctuation boundary)
  → PassThrough (punctuation boundary): 'Mẹ nồ: "Con phài học hành cham chỉ, dừng luờ biếng!" Tồ trả lờ: "Vâng,'

Input: ' ' - space
  → PassThrough (word boundary): 'Mẹ nồ: "Con phài học hành cham chỉ, dừng luờ biếng!" Tồ trả lờ: "Vâng, '

Input: 'c' - c
  → NoChange: 'Mẹ nồ: "Con phài học hành cham chỉ, dừng luờ biếng!" Tồ trả lờ: "Vâng, c'

Input: 'o' - o
  → NoChange: 'Mẹ nồ: "Con phài học hành cham chỉ, dừng luờ biếng!" Tồ trả lờ: "Vâng, co'

Input: 'n' - n
  → NoChange: 'Mẹ nồ: "Con phài học hành cham chỉ, dừng luờ biếng!" Tồ trả lờ: "Vâng, con'

Input: ' ' - space
  → PassThrough (word boundary): 'Mẹ nồ: "Con phài học hành cham chỉ, dừng luờ biếng!" Tồ trả lờ: "Vâng, con '

Input: 'h' - h
  → NoChange: 'Mẹ nồ: "Con phài học hành cham chỉ, dừng luờ biếng!" Tồ trả lờ: "Vâng, con h'

Input: 'i' - i
  → NoChange: 'Mẹ nồ: "Con phài học hành cham chỉ, dừng luờ biếng!" Tồ trả lờ: "Vâng, con hi'

Input: 'e' - e
  → NoChange: 'Mẹ nồ: "Con phài học hành cham chỉ, dừng luờ biếng!" Tồ trả lờ: "Vâng, con hie'

Input: 'e' - e
  → Replace (bs:3): 'hiê' → Current output: 'Mẹ nồ: "Con phài học hành cham chỉ, dừng luờ biếng!" Tồ trả lờ: "Vâng, con hiê'
  → Engine state reset after injection

Input: 'u' - u
  → NoChange: 'Mẹ nồ: "Con phài học hành cham chỉ, dừng luờ biếng!" Tồ trả lờ: "Vâng, con hiêu'

Input: 'r' - r (hieeur->hiểu)
  → Replace (bs:4): 'hiểu' → Current output: 'Mẹ nồ: "Con phài học hành cham chỉ, dừng luờ biếng!" Tồ trả lờ: "Vâng, con hiểu'
  → Engine state reset after injection

Input: ' ' - space
  → PassThrough (word boundary): 'Mẹ nồ: "Con phài học hành cham chỉ, dừng luờ biếng!" Tồ trả lờ: "Vâng, con hiểu '

Input: 'r' - r
  → NoChange: 'Mẹ nồ: "Con phài học hành cham chỉ, dừng luờ biếng!" Tồ trả lờ: "Vâng, con hiểu r'

Input: 'o' - o
  → NoChange: 'Mẹ nồ: "Con phài học hành cham chỉ, dừng luờ biếng!" Tồ trả lờ: "Vâng, con hiểu ro'

Input: 'o' - o
  → Replace (bs:2): 'rô' → Current output: 'Mẹ nồ: "Con phài học hành cham chỉ, dừng luờ biếng!" Tồ trả lờ: "Vâng, con hiểu rô'
  → Engine state reset after injection

Input: 'f' - f (roof->rồi)
  → Replace (bs:2): 'rồ' → Current output: 'Mẹ nồ: "Con phài học hành cham chỉ, dừng luờ biếng!" Tồ trả lờ: "Vâng, con hiểu rồ'
  → Engine state reset after injection

Input: '.' - period (punctuation boundary)
  → PassThrough (punctuation boundary): 'Mẹ nồ: "Con phài học hành cham chỉ, dừng luờ biếng!" Tồ trả lờ: "Vâng, con hiểu rồ.'

Input: '"' - closing quote (punctuation boundary)
  → PassThrough (punctuation boundary): 'Mẹ nồ: "Con phài học hành cham chỉ, dừng luờ biếng!" Tồ trả lờ: "Vâng, con hiểu rồ."'

🎯 Final Results:
================
Output: 'Mẹ nồ: "Con phài học hành cham chỉ, dừng luờ biếng!" Tồ trả lờ: "Vâng, con hiểu rồ."'
Words processed: 23
Punctuation boundaries: 10

❌ FAILURE: Output does not match expected result!
   Expected: 'Mẹ nói: "Con phải học hành chăm chỉ, đừng lười biếng!" Tôi trả lời: "Vâng, con hiểu rồi."'
   Actual:   'Mẹ nồ: "Con phài học hành cham chỉ, dừng luờ biếng!" Tồ trả lờ: "Vâng, con hiểu rồ."'

🔍 This indicates engine state corruption or boundary handling issues
✅ Test 4 Result: FAILED

📝 Test 5: Mixed Scenarios - Cross-Contamination Prevention
===========================================================
Testing Vietnamese transformations immediately before/after punctuation

Input: 'T' - T
  → NoChange: 'T'

Input: 'o' - o
  → NoChange: 'To'

Input: 'o' - o
  → Replace (bs:2): 'Tô' → Current output: 'Tô'
  → Engine state reset after injection

Input: 'f' - f (Toof->Tôi)
  → Replace (bs:2): 'Tồ' → Current output: 'Tồ'
  → Engine state reset after injection

Input: ' ' - space
  → PassThrough (word boundary): 'Tồ '

Input: 'a' - a
  → NoChange: 'Tồ a'

Input: 'w' - w (aw->ă)
  → Replace (bs:1): 'ă' → Current output: 'Tồ ă'
  → Engine state reset after injection

Input: 'n' - n
  → NoChange: 'Tồ ăn'

Input: ' ' - space
  → PassThrough (word boundary): 'Tồ ăn '

Input: 'c' - c
  → NoChange: 'Tồ ăn c'

Input: 'o' - o
  → NoChange: 'Tồ ăn co'

Input: 'w' - w
  → Replace (bs:2): 'cơ' → Current output: 'Tồ ăn cơ'
  → Engine state reset after injection

Input: 'm' - m (cowm->cơm)
  → NoChange: 'Tồ ăn cơm'

Input: ',' - comma - CRITICAL: should reset engine state
  → PassThrough (punctuation boundary): 'Tồ ăn cơm,'

Input: ' ' - space
  → PassThrough (word boundary): 'Tồ ăn cơm, '

Input: 'u' - u
  → NoChange: 'Tồ ăn cơm, u'

Input: 'o' - o
  → NoChange: 'Tồ ăn cơm, uo'

Input: 'n' - n
  → NoChange: 'Tồ ăn cơm, uon'

Input: 'g' - g (uong->uống)
  → NoChange: 'Tồ ăn cơm, uong'

Input: ' ' - space
  → PassThrough (word boundary): 'Tồ ăn cơm, uong '

Input: 'n' - n
  → NoChange: 'Tồ ăn cơm, uong n'

Input: 'u' - u
  → NoChange: 'Tồ ăn cơm, uong nu'

Input: 'o' - o
  → NoChange: 'Tồ ăn cơm, uong nuo'

Input: 'w' - w
  → Replace (bs:3): 'nuơ' → Current output: 'Tồ ăn cơm, uong nuơ'
  → Engine state reset after injection

Input: 'c' - c (nuowc->nước)
  → NoChange: 'Tồ ăn cơm, uong nuơc'

Input: '.' - period - CRITICAL: should reset engine state
  → PassThrough (punctuation boundary): 'Tồ ăn cơm, uong nuơc.'

Input: '\n' - newline
  → PassThrough (word boundary): 'Tồ ăn cơm, uong nuơc.\n'

Input: '"' - opening quote - CRITICAL: should reset engine state
  → PassThrough (punctuation boundary): 'Tồ ăn cơm, uong nuơc.\n"'

Input: 'X' - X
  → NoChange: 'Tồ ăn cơm, uong nuơc.\n"X'

Input: 'i' - i
  → NoChange: 'Tồ ăn cơm, uong nuơc.\n"Xi'

Input: 'n' - n
  → NoChange: 'Tồ ăn cơm, uong nuơc.\n"Xin'

Input: ' ' - space
  → PassThrough (word boundary): 'Tồ ăn cơm, uong nuơc.\n"Xin '

Input: 'c' - c
  → NoChange: 'Tồ ăn cơm, uong nuơc.\n"Xin c'

Input: 'h' - h
  → NoChange: 'Tồ ăn cơm, uong nuơc.\n"Xin ch'

Input: 'a' - a
  → NoChange: 'Tồ ăn cơm, uong nuơc.\n"Xin cha'

Input: 'f' - f (chaf->chà)
  → Replace (bs:3): 'chà' → Current output: 'Tồ ăn cơm, uong nuơc.\n"Xin chà'
  → Engine state reset after injection

Input: 'o' - o
  → NoChange: 'Tồ ăn cơm, uong nuơc.\n"Xin chào'

Input: '"' - closing quote - CRITICAL: should reset engine state
  → PassThrough (punctuation boundary): 'Tồ ăn cơm, uong nuơc.\n"Xin chào"'

Input: ' ' - space
  → PassThrough (word boundary): 'Tồ ăn cơm, uong nuơc.\n"Xin chào" '

Input: 'l' - l
  → NoChange: 'Tồ ăn cơm, uong nuơc.\n"Xin chào" l'

Input: 'a' - a
  → NoChange: 'Tồ ăn cơm, uong nuơc.\n"Xin chào" la'

Input: 'f' - f (laf->là)
  → Replace (bs:2): 'là' → Current output: 'Tồ ăn cơm, uong nuơc.\n"Xin chào" là'
  → Engine state reset after injection

Input: ' ' - space
  → PassThrough (word boundary): 'Tồ ăn cơm, uong nuơc.\n"Xin chào" là '

Input: 'c' - c
  → NoChange: 'Tồ ăn cơm, uong nuơc.\n"Xin chào" là c'

Input: 'a' - a
  → NoChange: 'Tồ ăn cơm, uong nuơc.\n"Xin chào" là ca'

Input: 'u' - u
  → NoChange: 'Tồ ăn cơm, uong nuơc.\n"Xin chào" là cau'

Input: ' ' - space
  → PassThrough (word boundary): 'Tồ ăn cơm, uong nuơc.\n"Xin chào" là cau '

Input: 't' - t
  → NoChange: 'Tồ ăn cơm, uong nuơc.\n"Xin chào" là cau t'

Input: 'r' - r
  → NoChange: 'Tồ ăn cơm, uong nuơc.\n"Xin chào" là cau tr'

Input: 'a' - a
  → NoChange: 'Tồ ăn cơm, uong nuơc.\n"Xin chào" là cau tra'

Input: 'r' - r (trar->trả)
  → Replace (bs:3): 'trả' → Current output: 'Tồ ăn cơm, uong nuơc.\n"Xin chào" là cau trả'
  → Engine state reset after injection

Input: ' ' - space
  → PassThrough (word boundary): 'Tồ ăn cơm, uong nuơc.\n"Xin chào" là cau trả '

Input: 'l' - l
  → NoChange: 'Tồ ăn cơm, uong nuơc.\n"Xin chào" là cau trả l'

Input: 'o' - o
  → NoChange: 'Tồ ăn cơm, uong nuơc.\n"Xin chào" là cau trả lo'

Input: 'w' - w
  → Replace (bs:2): 'lơ' → Current output: 'Tồ ăn cơm, uong nuơc.\n"Xin chào" là cau trả lơ'
  → Engine state reset after injection

Input: 'f' - f (lowf->lời)
  → Replace (bs:2): 'lờ' → Current output: 'Tồ ăn cơm, uong nuơc.\n"Xin chào" là cau trả lờ'
  → Engine state reset after injection

Input: ':' - colon - CRITICAL: should reset engine state
  → PassThrough (punctuation boundary): 'Tồ ăn cơm, uong nuơc.\n"Xin chào" là cau trả lờ:'

Input: ' ' - space
  → PassThrough (word boundary): 'Tồ ăn cơm, uong nuơc.\n"Xin chào" là cau trả lờ: '

Input: '"' - opening quote
  → PassThrough (punctuation boundary): 'Tồ ăn cơm, uong nuơc.\n"Xin chào" là cau trả lờ: "'

Input: 'K' - K
  → NoChange: 'Tồ ăn cơm, uong nuơc.\n"Xin chào" là cau trả lờ: "K'

Input: 'h' - h
  → NoChange: 'Tồ ăn cơm, uong nuơc.\n"Xin chào" là cau trả lờ: "Kh'

Input: 'o' - o
  → NoChange: 'Tồ ăn cơm, uong nuơc.\n"Xin chào" là cau trả lờ: "Kho'

Input: 'o' - o
  → Replace (bs:3): 'Khô' → Current output: 'Tồ ăn cơm, uong nuơc.\n"Xin chào" là cau trả lờ: "Khô'
  → Engine state reset after injection

Input: 'n' - n
  → NoChange: 'Tồ ăn cơm, uong nuơc.\n"Xin chào" là cau trả lờ: "Khôn'

Input: 'g' - g (Khoong->Không)
  → NoChange: 'Tồ ăn cơm, uong nuơc.\n"Xin chào" là cau trả lờ: "Không'

Input: ' ' - space
  → PassThrough (word boundary): 'Tồ ăn cơm, uong nuơc.\n"Xin chào" là cau trả lờ: "Không '

Input: 's' - s
  → NoChange: 'Tồ ăn cơm, uong nuơc.\n"Xin chào" là cau trả lờ: "Không s'

Input: 'a' - a
  → NoChange: 'Tồ ăn cơm, uong nuơc.\n"Xin chào" là cau trả lờ: "Không sa'

Input: 'o' - o (sao->sao)
  → NoChange: 'Tồ ăn cơm, uong nuơc.\n"Xin chào" là cau trả lờ: "Không sao'

Input: '?' - question mark
  → PassThrough (punctuation boundary): 'Tồ ăn cơm, uong nuơc.\n"Xin chào" là cau trả lờ: "Không sao?'

Input: '"' - closing quote
  → PassThrough (punctuation boundary): 'Tồ ăn cơm, uong nuơc.\n"Xin chào" là cau trả lờ: "Không sao?"'

🎯 Final Results:
================
Output: 'Tồ ăn cơm, uong nuơc.\n"Xin chào" là cau trả lờ: "Không sao?"'
Words processed: 11
Punctuation boundaries: 8

❌ FAILURE: Output does not match expected result!
   Expected: 'Tôi ăn cơm, uống nước.\n"Xin chào" là câu trả lời: "Không sao?"'
   Actual:   'Tồ ăn cơm, uong nuơc.\n"Xin chào" là cau trả lờ: "Không sao?"'

🔍 This indicates engine state corruption or boundary handling issues
✅ Test 5 Result: FAILED

🎉 All Vietnamese engine state reset tests completed!
