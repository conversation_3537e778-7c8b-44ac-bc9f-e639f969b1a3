use openkey_rust_lib::engine::VietnameseEngine;
use openkey_rust_lib::engine::traits::ProcessResult;

fn main() {
    println!("🧪 Testing Word Boundary Transformations");
    println!("=========================================");
    
    let mut engine = VietnameseEngine::new();
    
    // Test "uo" followed by space (word boundary)
    println!("\n🔍 Testing 'uo ' (uo + space):");
    let mut result = String::new();
    
    // Process each character
    match engine.process_character('u') {
        ProcessResult::NoChange => result.push('u'),
        ProcessResult::Replace { backspace_count, new_chars } => {
            for _ in 0..backspace_count { result.pop(); }
            result.extend(new_chars);
        }
        _ => {}
    }
    println!("  After 'u': '{}'", result);
    
    match engine.process_character('o') {
        ProcessResult::NoChange => result.push('o'),
        ProcessResult::Replace { backspace_count, new_chars } => {
            for _ in 0..backspace_count { result.pop(); }
            result.extend(new_chars);
        }
        _ => {}
    }
    println!("  After 'o': '{}'", result);
    
    match engine.process_character(' ') {
        ProcessResult::NoChange => result.push(' '),
        ProcessResult::Replace { backspace_count, new_chars } => {
            for _ in 0..backspace_count { result.pop(); }
            result.extend(new_chars);
        }
        ProcessResult::PassThrough => result.push(' '),
        _ => {}
    }
    println!("  After ' ': '{}'", result);
    
    if result.trim() == "ư" {
        println!("  ✅ PASS: 'uo' → 'ư' at word boundary");
    } else {
        println!("  ❌ FAIL: 'uo' → '{}' (expected: 'ư')", result.trim());
    }
    
    // Test "uongs" (should not transform "uo" during processing)
    println!("\n🔍 Testing 'uongs' (should preserve 'uo' for tone processing):");
    let mut engine2 = VietnameseEngine::new();
    let mut result2 = String::new();
    
    for ch in "uongs".chars() {
        match engine2.process_character(ch) {
            ProcessResult::NoChange => result2.push(ch),
            ProcessResult::Replace { backspace_count, new_chars } => {
                for _ in 0..backspace_count { result2.pop(); }
                result2.extend(new_chars);
            }
            _ => {}
        }
        println!("  After '{}': '{}'", ch, result2);
    }
    
    if result2 == "uống" {
        println!("  ✅ PASS: 'uongs' → 'uống'");
    } else {
        println!("  ❌ FAIL: 'uongs' → '{}' (expected: 'uống')", result2);
    }
}
