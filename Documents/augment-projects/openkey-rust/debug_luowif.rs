use openkey_rust_lib::engine::{VietnameseEngine, InputMethod};
use openkey_rust_lib::engine::traits::ProcessResult;

fn main() {
    println!("🔍 Debug 'luowif' → 'lười' Transformation");
    println!("==========================================");
    
    let mut engine = VietnameseEngine::new();
    engine.set_input_method(InputMethod::Telex);
    
    let sequence = "luowif";
    let mut current_output = String::new();
    
    println!("Testing '{}' step by step:", sequence);
    println!("Expected: l -> lu -> luo -> luơ -> lu<PERSON>i -> lư<PERSON>i");
    println!();
    
    for (i, ch) in sequence.chars().enumerate() {
        println!("Step {}: Processing '{}'", i + 1, ch);
        println!("  Current output: '{}'", current_output);
        // println!("  Buffer state: {}", engine.get_buffer_debug());
        
        let result = engine.process_character(ch);
        
        match result {
            ProcessResult::NoChange => {
                current_output.push(ch);
                println!("  Result: NoChange -> '{}'", current_output);
            }
            ProcessResult::Replace { backspace_count, new_chars } => {
                // Remove backspace_count characters from current_output
                let chars: Vec<char> = current_output.chars().collect();
                if chars.len() >= backspace_count as usize {
                    current_output = chars[..chars.len() - backspace_count as usize].iter().collect();
                }
                // Add new characters
                for new_ch in new_chars {
                    current_output.push(new_ch);
                }
                println!("  Result: Replace (bs:{}) -> '{}'", backspace_count, current_output);
                
                // Simulate the reset that happens after injection
                engine.reset_after_injection();
                println!("  → Engine state reset after injection");
            }
            ProcessResult::PassThrough => {
                current_output.push(ch);
                println!("  Result: PassThrough -> '{}'", current_output);
            }
            _ => {
                current_output.push(ch);
                println!("  Result: Other -> '{}'", current_output);
            }
        }
        println!();
    }
    
    println!("Final result: '{}'", current_output);
    println!("Expected: 'lười'");
    println!("Success: {}", current_output == "lười");
    
    // Let's also test what the direct transformation gives us
    println!();
    println!("=== Direct Transformation Test ===");
    let mut engine2 = VietnameseEngine::new();
    engine2.set_input_method(InputMethod::Telex);
    
    // Process the sequence character by character to get the final result
    let mut direct_result = String::new();
    for ch in "luowif".chars() {
        let result = engine2.process_character(ch);
        match result {
            ProcessResult::NoChange => direct_result.push(ch),
            ProcessResult::Replace { backspace_count, new_chars } => {
                let chars: Vec<char> = direct_result.chars().collect();
                if chars.len() >= backspace_count as usize {
                    direct_result = chars[..chars.len() - backspace_count as usize].iter().collect();
                }
                for new_ch in new_chars {
                    direct_result.push(new_ch);
                }
                engine2.reset_after_injection();
            }
            ProcessResult::PassThrough => direct_result.push(ch),
            _ => direct_result.push(ch),
        }
    }
    println!("Direct transformation: 'luowif' → '{}'", direct_result);
    
    // Let's also test some variations to understand the pattern
    println!();
    println!("=== Pattern Analysis ===");
    let test_sequences = vec![
        "luow",    // Should be "luơ"
        "luowi",   // Should be "luơi" 
        "luowif",  // Should be "lười"
        "uowif",   // Should be "ười"
        "owif",    // Should be "ời"
    ];
    
    for seq in test_sequences {
        let mut engine3 = VietnameseEngine::new();
        engine3.set_input_method(InputMethod::Telex);

        let mut result = String::new();
        for ch in seq.chars() {
            let process_result = engine3.process_character(ch);
            match process_result {
                ProcessResult::NoChange => result.push(ch),
                ProcessResult::Replace { backspace_count, new_chars } => {
                    let chars: Vec<char> = result.chars().collect();
                    if chars.len() >= backspace_count as usize {
                        result = chars[..chars.len() - backspace_count as usize].iter().collect();
                    }
                    for new_ch in new_chars {
                        result.push(new_ch);
                    }
                    engine3.reset_after_injection();
                }
                ProcessResult::PassThrough => result.push(ch),
                _ => result.push(ch),
            }
        }

        println!("'{}' → '{}'", seq, result);
    }
}
