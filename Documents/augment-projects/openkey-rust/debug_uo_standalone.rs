use openkey_rust_lib::engine::vietnamese::VietnameseProcessor;
use openkey_rust_lib::engine::ProcessResult;

fn main() {
    println!("🔍 Testing standalone 'uo' transformation");
    
    // Test 1: "uo" without space (current failing case)
    println!("\n=== Test 1: 'uo' (no space) ===");
    let mut processor1 = VietnameseProcessor::new();
    let mut result1 = String::new();
    
    for ch in "uo".chars() {
        let result = processor1.process_character(ch);
        match result {
            ProcessResult::NoChange => result1.push(ch),
            ProcessResult::Replace { new_chars, backspace_count } => {
                for _ in 0..backspace_count { result1.pop(); }
                result1.extend(new_chars.iter());
            },
            ProcessResult::ReplaceText { text, backspace_count } => {
                for _ in 0..backspace_count { result1.pop(); }
                result1.push_str(&text);
            },
            ProcessResult::PassThrough => result1.push(ch),
            _ => result1.push(ch),
        }
    }
    println!("Result: '{}' (expected: 'ư')", result1);
    
    // Test 2: "uo " with space (should work)
    println!("\n=== Test 2: 'uo ' (with space) ===");
    let mut processor2 = VietnameseProcessor::new();
    let mut result2 = String::new();
    
    for ch in "uo ".chars() {
        let result = processor2.process_character(ch);
        match result {
            ProcessResult::NoChange => result2.push(ch),
            ProcessResult::Replace { new_chars, backspace_count } => {
                for _ in 0..backspace_count { result2.pop(); }
                result2.extend(new_chars.iter());
            },
            ProcessResult::ReplaceText { text, backspace_count } => {
                for _ in 0..backspace_count { result2.pop(); }
                result2.push_str(&text);
            },
            ProcessResult::PassThrough => result2.push(ch),
            _ => result2.push(ch),
        }
    }
    println!("Result: '{}' (expected: 'ư ')", result2);
    
    // Test 3: Check if we need to trigger transformation differently
    println!("\n=== Test 3: Manual word boundary trigger ===");
    let mut processor3 = VietnameseProcessor::new();
    let mut result3 = String::new();
    
    // Process "uo"
    for ch in "uo".chars() {
        let result = processor3.process_character(ch);
        match result {
            ProcessResult::NoChange => result3.push(ch),
            ProcessResult::Replace { new_chars, backspace_count } => {
                for _ in 0..backspace_count { result3.pop(); }
                result3.extend(new_chars.iter());
            },
            ProcessResult::ReplaceText { text, backspace_count } => {
                for _ in 0..backspace_count { result3.pop(); }
                result3.push_str(&text);
            },
            ProcessResult::PassThrough => result3.push(ch),
            _ => result3.push(ch),
        }
    }
    
    // Now trigger word boundary with a space
    let result = processor3.process_character(' ');
    match result {
        ProcessResult::NoChange => result3.push(' '),
        ProcessResult::Replace { new_chars, backspace_count } => {
            for _ in 0..backspace_count { result3.pop(); }
            result3.extend(new_chars.iter());
        },
        ProcessResult::ReplaceText { text, backspace_count } => {
            for _ in 0..backspace_count { result3.pop(); }
            result3.push_str(&text);
        },
        ProcessResult::PassThrough => result3.push(' '),
        _ => result3.push(' '),
    }
    
    println!("Result: '{}' (expected: 'ư ')", result3);
}
